# Use the official Maven image to build the application
FROM maven:3.8.4-openjdk-11 AS build

# Set the working directory
WORKDIR /app

# Copy the pom.xml and download the dependencies
COPY pom.xml .
RUN mvn dependency:go-offline

# Copy the source code
COPY src ./src

# Build the application
RUN mvn clean install

# Use a smaller JDK runtime image to run the application
FROM openjdk:11-jre-slim

# Set the working directory
WORKDIR /app

# Copy the built WAR file from the build stage
COPY --from=build /app/target/messaging-0.0.1-SNAPSHOT.war .

# Expose the port your application will run on
EXPOSE 8089

# Run the application using java -jar
CMD ["java", "-jar", "messaging-0.0.1-SNAPSHOT.war"]