package co.bw.hrdc.weblogic;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.netflix.eureka.EnableEurekaClient;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

@EnableSwagger2
@EnableEurekaClient
@SpringBootApplication
public class MessagingApplication {
	private static final Logger logger = LoggerFactory.getLogger(MessagingApplication.class);

	public static void main(String[] args) {
		logger.info("Communication Service initiated");

		SpringApplication.run(MessagingApplication.class, args);
	}

}
