package bw.org.hrdf.account.models.otp;

import bw.org.hrdf.account.entity.enums.OTPTypes;
import bw.org.hrdf.account.entity.enums.OtpStatus;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class OtpResponse {
    private Long id;
    private String uuid;
    private Integer otp;
    private OTPTypes type;
    private LocalDateTime lifetime;
    private OtpStatus status;
    private String reference;
    private Integer requestCount = 1;
    private LocalDateTime lastUpdatedAt;
}
