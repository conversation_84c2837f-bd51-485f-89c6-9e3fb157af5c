package bw.org.hrdf.account.entity.company;

import bw.org.hrdf.account.entity.Base;
import bw.org.hrdf.account.entity.enums.VerificationStatusEnum;
import bw.org.hrdf.account.entity.enums.VerificationTypeEnum;
import com.fasterxml.jackson.annotation.JsonBackReference;
import jakarta.persistence.*;
import lombok.*;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * eHRDF Verification Object representation
 * <AUTHOR>
 *
 */
@Entity
@Getter @Setter @AllArgsConstructor @NoArgsConstructor
@Table(name = "verifications")
public class Verification extends Base implements Serializable {

	@Column(name = "reference_number", nullable = false)
	private String reference;

	@Column(name = "verification_type", nullable = false)
	@Enumerated(EnumType.STRING)
	private VerificationTypeEnum type;

	@Column(name = "status", nullable = false)
	@Enumerated(EnumType.STRING)
	private VerificationStatusEnum status;

	@Column(name = "verified_at")
	private LocalDateTime verifiedAt;

	@Column(name = "expiry_date")
	private Date expiryDate;

	@ManyToOne(cascade = CascadeType.ALL, optional = false)
	@JoinColumn(name = "company_id", nullable = false)
	@JsonBackReference
	@ToString.Exclude
	private CompanyEntity company;
}
