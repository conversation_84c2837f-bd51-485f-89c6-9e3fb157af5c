package bw.org.hrdf.account.repositories.company;

import bw.org.hrdf.account.entity.company.Actions;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface ActionsRepository extends JpaRepository<Actions, Long> {
    @Query("SELECT a FROM Actions a WHERE a.uuid = :uuid")
    Optional<Actions> findByUuid(String uuid);
//    List<Actions> findByRegistrationApplicationId(Long applicationId);
}
