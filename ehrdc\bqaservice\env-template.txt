SERVER_PORT=
DB_HOST=
DB_NAME=
DB_USERNAME=
DB_PASSWORD=
CONFIG_SERVER_URL=
KC_ISSUER_URI=
KC_REALM=
KC_CLIENT_ID=
KC_CLIENT_SECRETE=
KC_ADMIN_USERNAME=
KC_ADMIN_PASSWORD=






spring:
  application:
    name: authService
  profiles:
    active: "local"
  datasource:
    url: jdbc:postgresql://${DB_HOST:localhost:5432}/${DB_NAME:ehrdcf_uaa}
    username: ${DB_USERNAME:postgres}
    password: ${DB_PASSWORD:administrator}
    driver-class-name: org.postgresql.Driver
    hikari:
      maximum-pool-size: 10
      minimum-idle: 5
      pool-name: HikariCP
      idle-timeout: 30000
      max-lifetime: 60000
      connection-timeout: 30000
  jpa:
    hibernate:
      ddl-auto: update  #'none', 'validate', 'update', 'create', or 'create-drop'
    show-sql: true  # Set to true to log the SQL queries to the console
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
  config:
    import: optional:configserver:${CONFIG_SERVER_URL:http://localhost:8071/}
  # Security Configuration
  security:
    oauth2:
      client:
        registration:
          keycloak:
            client-id: ${KC_CLIENT_ID:ehrdcf}
            client-secret: ${KC_CLIENT_SECRETE:1ariTRLyhqL1i8REEwlhQlJT2lQgkbKd}
            authorization-grant-type: authorization_code
            scope: openid,profile,email
        provider:
          keycloak:
            issuer-uri: ${KC_ISSUER_URI:http://localhost:9090/realms/hrdc}

      resourceserver:
        jwt:
          issuer-uri: ${KC_ISSUER_URI:http://localhost:9090}/realms/hrdc}
          jwk-set-uri: ${spring.security.oauth2.resourceserver.jwt.issuer-uri}/protocol/openid-connect/certs
  # JWT Configuration
jwt:
  auth:
    converter:
      resource-id: ${KC_CLIENT_ID:ehrdcf}
      principal-attribute: principal_username

keycloak:
  server-url: ${KC_ISSUER_URI:http://localhost:9090}
  realm: ${KC_REALM:hrdc}
  client-id: ${KC_CLIENT_ID:ehrdcf}
  client-secret: ${KC_CLIENT_SECRETE:1ariTRLyhqL1i8REEwlhQlJT2lQgkbKd}
  admin-username: ${KC_ADMIN_USERNAME:admin}
  admin-password: ${KC_ADMIN_PASSWORD:admin}

#Base path
server:
  port: ${SERVER_PORT}

# Logging Configuration
logging:
  level:
    org.springframework.security: DEBUG