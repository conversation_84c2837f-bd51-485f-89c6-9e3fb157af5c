package bw.org.hrdf.account.api;

import bw.org.hrdf.account.helper.ApiResponse;
import bw.org.hrdf.account.models.otp.OtpRequest;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestBody;

@Component
public class OtpClientFallback implements OtpClient {

    @Override
    public ResponseEntity<?> generateOtp(@RequestBody OtpRequest otpRequest){
        return ResponseEntity.status(HttpStatus.SERVICE_UNAVAILABLE)
                .body(new ApiResponse<>(false, "OTP Service is unavailable", null, null));
    }

    @Override
    public ApiResponse<?> verifyOtp(@RequestBody OtpRequest otpRequest){
        return new ApiResponse<>(
                false,
                "OTP Service is unavailable",
                null,
                null
        );
    }
}
