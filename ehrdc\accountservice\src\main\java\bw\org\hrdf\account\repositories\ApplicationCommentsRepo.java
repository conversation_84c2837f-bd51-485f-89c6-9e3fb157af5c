package bw.org.hrdf.account.repositories;

import bw.org.hrdf.account.entity.ApplicationComments;
import lombok.NonNull;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @CreatedOn 21/03/25 15:48
 * @UpdatedBy martinspectre
 * @UpdatedOn 21/03/25 15:48
 */
@Repository
public interface ApplicationCommentsRepo extends JpaRepository<ApplicationComments, Long> {
    @Query(value = "SELECT * FROM application_comments a WHERE a.application_id = :applicationId", nativeQuery = true)
    @NonNull
    List<ApplicationComments> findByCommentsApplicationId(@NonNull String applicationId);
}
