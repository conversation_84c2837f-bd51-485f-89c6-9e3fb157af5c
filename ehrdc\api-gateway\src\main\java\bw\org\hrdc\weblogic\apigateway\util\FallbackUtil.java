package bw.org.hrdc.weblogic.apigateway.util;

import org.springframework.http.MediaType;
import org.springframework.web.reactive.function.server.ServerResponse;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

import reactor.core.publisher.Mono;

public class FallbackUtil {

    /**
     * Generates a fallback response with metadata.
     *
     * @param serviceName the name of the service for which the fallback is invoked
     * @param message     the fallback message to be returned
     * @param errorCode   an optional error code
     * @return a ServerResponse with the metadata and provided message
     */
    public static Mono<ServerResponse> generateFallbackResponse(String serviceName, String message, String errorCode) {
        Map<String, Object> response = new HashMap<>();
        response.put("timestamp", LocalDateTime.now());
        response.put("serviceName", serviceName);
        response.put("message", message);
        if (errorCode != null && !errorCode.isEmpty()) {
            response.put("errorCode", errorCode);
        }
        return ServerResponse.ok()
                .contentType(MediaType.APPLICATION_JSON)
                .bodyValue(response);
    }
}

