package bw.org.hrdf.scheduler;

import bw.org.hrdf.service.UserSettingsService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Component
public class AccountPolicyScheduler {

    private static final Logger LOGGER = LoggerFactory.getLogger(AccountPolicyScheduler.class);

    @Autowired
    private UserSettingsService userSettingsService;

    @Scheduled(cron = "0 0 * * * ?") // Runs hourly
    public void invalidateTokens() {
        LOGGER.info("Starting account unlocking job...");
        try {
            int loginAttempts = userSettingsService.updateExpiredLoginAttempts();

            LOGGER.info("Successfully invalidated {} expired tokens.", loginAttempts);
        } catch (Exception e) {
            LOGGER.error("Error occurred during account unlocking job: {}", e.getMessage(), e);
        }
    }
}

