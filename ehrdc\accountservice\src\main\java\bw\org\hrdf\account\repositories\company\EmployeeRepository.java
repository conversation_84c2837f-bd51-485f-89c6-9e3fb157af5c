package bw.org.hrdf.account.repositories.company;

import bw.org.hrdf.account.entity.company.Employee;
import lombok.NonNull;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface EmployeeRepository extends JpaRepository<Employee, Long>, JpaSpecificationExecutor<Employee> {

    @Query("SELECT e FROM Employee e WHERE e.uuid = :uuid")
    @NonNull
    Optional<Employee> findByUuid(@NonNull String uuid);
}
