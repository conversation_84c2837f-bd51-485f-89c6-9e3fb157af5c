package bw.org.hrdf.entity;

import bw.org.hrdf.entity.enums.OTPTypes;
import bw.org.hrdf.entity.enums.OtpStatus;
import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "otp")
@Data
public class OtpEntity extends Base{

    @Column
    private Integer otp;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false, name="type")
    private OTPTypes type = OTPTypes.ACCOUNT_VERIFICATION;

    @Column
    private LocalDateTime lifetime;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private OtpStatus status = OtpStatus.ACTIVE;

    @Column(name = "reference", nullable = false)
    private String reference;

    @Column(nullable = false)
    private Integer requestCount = 1;

    @Column(nullable = false)
    private LocalDateTime lastUpdatedAt;

    @PrePersist
    public void prePersist() {
        this.lastUpdatedAt = LocalDateTime.now();
    }

    @PreUpdate
    public void preUpdate() {
        this.lastUpdatedAt = LocalDateTime.now();
    }

}
