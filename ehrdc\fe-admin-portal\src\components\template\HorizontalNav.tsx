import HorizontalMenuContent from './HorizontalMenuContent'
import useResponsive from '@/utils/hooks/useResponsive'
import { RootState } from "@/store";
import { useSelector } from 'react-redux'

const HorizontalNav = () => {
    const mode = useSelector((state: RootState) => state.theme.mode)
    const userAuthority: string[] | undefined = [] //useSelector((state: RootState) => state.auth.user.authority)

    const { larger } = useResponsive()

    return (
        <>
            {larger.md && (
                <HorizontalMenuContent
                    manuVariant={mode}
                    userAuthority={userAuthority}
                />
            )}
        </>
    )
}

export default HorizontalNav
