import { ActionTypes as actionTypes } from './actionTypes'
import { Filter } from '@/views/modules/appeals/types'

// Generic Action Interface
interface Action<T = any> {
    type: string;
    payload?: T;
}



//Fetch recognition applications
const getAppeals = (pageNumber: number, size: number, filter: Filter): Action => ({
    type: actionTypes.FETCH_APPEALS_REQUESTS,
    payload: { pageNumber, size, filter }
})

// Exported Actions and Events
export const actions = {
    getAppeals
}

export const events = {
    fetch: {
        REQUEST: actionTypes.FETCH_APPEALS_REQUESTS,
        RECEIVED: actionTypes.FETCH_APPEALS_REQUESTS_SUCCESS,
        FAILED: actionTypes.FETCH_APPEALS_REQUESTS_FAILED
    },
    comment: {
        REQUEST: actionTypes.SUBMIT_APPEAL_COMMENT_REQUEST,
        RECEIVED: actionTypes.SUBMIT_APPEAL_COMMENT_SUCCESS,
        FAILED: actionTypes.SUBMIT_APPEAL_COMMENT_FAILED
    },
    upload: {
        REQUEST: actionTypes.UPLOAD_APPEAL_ATTACHMENT_REQUEST,
        RECEIVED: actionTypes.UPLOAD_APPEAL_ATTACHMENT_SUCCESS,
        FAILED: actionTypes.UPLOAD_APPEAL_ATTACHMENT_FAILED
    }
}
