package bw.org.hrdf.boilerplate.dto;

import bw.org.hrdf.boilerplate.entity.CourseTypesEnum;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;

@Data
public class ProgrammesDTO {
    private String uuid;
    private String ProgrammeName;
    private Integer noOfLearners;
    private String courseType;
    private String awardingBoard;
    private LocalDateTime dateOfAccreditation;
    private Date expiryDate;
    private String module;
}
