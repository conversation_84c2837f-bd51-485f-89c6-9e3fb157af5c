package com.workflowenginee.workflow.delegate.complaint;

import java.util.HashMap;
import java.util.Map;

import org.flowable.engine.delegate.DelegateExecution;
import org.flowable.engine.delegate.JavaDelegate;
import org.springframework.stereotype.Component;

import com.workflowenginee.workflow.api.WorkplaceLearningClient;
import com.workflowenginee.workflow.dto.NotifyUsersByRoleDto;
import com.workflowenginee.workflow.service.NotificationComplaintService;
import com.workflowenginee.workflow.util.ApiResponse;
import com.workflowenginee.workflow.util.Enums;

@Component("fetchAfterEscalationDelegate")
public class FetchAfterEscalationDelegate implements JavaDelegate {

    private final WorkplaceLearningClient workplaceLearningClient;
    private final NotificationComplaintService notificationComplaintService;

    public FetchAfterEscalationDelegate(WorkplaceLearningClient workplaceLearningClient,
                                      NotificationComplaintService notificationComplaintService) {
        this.workplaceLearningClient = workplaceLearningClient;
        this.notificationComplaintService = notificationComplaintService;
    }

    @Override
    public void execute(DelegateExecution execution) {
        String processInstanceId = execution.getProcessInstanceId();
        String complaintId = (String) execution.getVariable("complaintId");
        String role = (String) execution.getVariable("role");

        System.out.println("[Process: " + processInstanceId + "] Fetching complaint data after escalation: " + complaintId);

        try {
            // Fetch updated complaint data
            ApiResponse<?> response = workplaceLearningClient.getComplaintById(complaintId);

            if (response != null && response.getData() instanceof Map) {
                Map<String, Object> complaintData = (Map<String, Object>) response.getData();
                execution.setVariable("escalatedComplaintData", complaintData);
                execution.setVariable("escalationLevel", "AGENT_LEAD");
                execution.setVariable("escalatedAt", System.currentTimeMillis());

                System.out.println("[Process: " + processInstanceId + "] Successfully fetched escalated complaint data");

                // TODO: Notify agent lead about escalation
                System.out.println("[Process: " + processInstanceId + "] Agent lead would be notified about escalation");

                // Set default escalation decision - will be overridden by actual agent lead action
                execution.setVariable("escalationDecision", "pending");

            } else {
                System.err.println("[Process: " + processInstanceId + "] Failed to fetch escalated complaint data");
                
                // Set fallback data
                Map<String, Object> fallbackData = new HashMap<>();
                fallbackData.put("complaintId", complaintId);
                fallbackData.put("status", "ESCALATED");
                fallbackData.put("error", "Failed to fetch escalated complaint details");
                
                execution.setVariable("escalatedComplaintData", fallbackData);
                execution.setVariable("escalationDecision", "error");
            }

        } catch (Exception e) {
            System.err.println("[Process: " + processInstanceId + "] Error fetching escalated complaint data: " + e.getMessage());
            e.printStackTrace();

            // Set error data
            Map<String, Object> errorData = new HashMap<>();
            errorData.put("complaintId", complaintId);
            errorData.put("status", "ERROR");
            errorData.put("error", e.getMessage());
            
            execution.setVariable("escalatedComplaintData", errorData);
            execution.setVariable("escalationDecision", "error");
        }
    }
}
