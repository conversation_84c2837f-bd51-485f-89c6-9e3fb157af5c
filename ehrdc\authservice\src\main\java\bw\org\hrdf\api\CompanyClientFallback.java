package bw.org.hrdf.api;

import bw.org.hrdf.helper.ApiResponse;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestParam;

@Component
public class CompanyClientFallback implements CompanyClient {

    @Override
    public ApiResponse<?> fetchCompanyIdentifier(@RequestParam String userId){
        return new ApiResponse<>(
                false,
                "Account Service is unavailable",
                null,
                null
        );
    }
}
