package bw.org.hrdf.audit;

import jakarta.servlet.http.HttpServletRequest;
import org.springframework.data.domain.AuditorAware;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.util.Objects;
import java.util.Optional;

/**
 * Created by <PERSON>
 */

@Component("auditAwareImpl")
public class AuditAwareImpl implements AuditorAware<String> {

    private static final String SYSTEM_AUDITOR = "SYSTEM";
    private static final String AUTH_HEADER = "X-Authenticated-User";

    @Override
    public Optional<String> getCurrentAuditor() {
        // Extract the user from the custom header set by the API gateway
        String username = getAuthenticatedUsernameFromHeader();
        return Optional.ofNullable(username).filter(user -> !user.isEmpty()).or(() -> Optional.of(SYSTEM_AUDITOR));
    }

    private String getAuthenticatedUsernameFromHeader() {
        // Use RequestContextHolder to get the current HTTP request
        HttpServletRequest request = ((ServletRequestAttributes) Objects.requireNonNull(RequestContextHolder.getRequestAttributes())).getRequest();

        // Extract the user information from the custom header
        return request.getHeader(AUTH_HEADER);

    }
}

