package bw.org.hrdf.account.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "user_company")
@Data
public class UserCompany extends Base{
    @Column(nullable = false, unique = true)
    private String userId;
    @Column(nullable = false)
    private String companyId;
}

