package bw.org.hrdf.account.repositories.company;

import bw.org.hrdf.account.entity.company.ApplicationEntity;
import bw.org.hrdf.account.entity.enums.StateEnum;
import bw.org.hrdf.account.entity.enums.StatusEnum;
import bw.org.hrdf.account.entity.enums.OrganisationCategory;
import bw.org.hrdf.account.entity.enums.OrganisationTypeEnum;
import jakarta.transaction.Transactional;
import org.springframework.data.jpa.repository.JpaRepository;

import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.Optional;

@Repository
public interface ApplicationRepository extends JpaRepository<ApplicationEntity, Long> {

    @Query("SELECT a FROM ApplicationEntity a WHERE a.referenceNumber = :referenceNumber")
    Optional<ApplicationEntity> findByReferenceNumber(String referenceNumber);

    @Query("SELECT a FROM ApplicationEntity a WHERE a.uuid = :id")
    Optional<ApplicationEntity> findByUuid(@Param("id") String uuid);

    @Modifying
    @Transactional
    @Query("UPDATE ApplicationEntity a SET " +
            "a.status = :status, " +
            "a.state = :state, " +
            "a.applicationSubmissionDate = :submissionDate, " +
            "a.agentLead = :agentLead, " +
            "a.agent = :agent, " +
            "a.manager = :manager " +
            "WHERE a.uuid = :applicationId")
    int updateApplication(String applicationId, StatusEnum status, StateEnum state,
                          LocalDateTime submissionDate, String agentLead,
                          String agent, String manager);
    

    @Transactional
    @Modifying
    @Query("UPDATE ApplicationEntity a SET " +
            "a.updatedAt = CURRENT_TIMESTAMP, " +
            "a.agent = CASE WHEN :role = 'AGENT' THEN :userId ELSE a.agent END, " +
            "a.agentLead = CASE WHEN :role = 'AGENT_LEAD' THEN :userId ELSE a.agentLead END, " +
            "a.manager = CASE WHEN :role = 'MANAGER' THEN :userId ELSE a.manager END, " +
            "a.state = " +
            "   CASE " +
            "       WHEN :role = 'AGENT' THEN 'IN_PROCESSING' " +
            "       WHEN :role = 'AGENT_LEAD' THEN 'SUBMITTED' " +
            "       WHEN :role = 'OFFICER' THEN 'IN_REVIEW' " +
            "       WHEN :role = 'OFFICER_LEAD' THEN 'IN_REVIEW' " +
            "       WHEN :role = 'MANAGER' THEN 'APPROVAL' " +
            "       ELSE a.state " +
            "   END, " +
            "a.status = 'PENDING' " +
            "WHERE LOWER(a.referenceNumber) = LOWER(:referenceNumber)")
    int updateApplicationAssignedUser(@Param("referenceNumber") String reference, @Param("role") String role, @Param("userId") String userId);

    @Transactional
    @Modifying
    @Query("UPDATE ApplicationEntity a SET " +
            "a.updatedAt = CURRENT_TIMESTAMP, " +
            "a.state = :state, " +
            "a.status = :status " +
            "WHERE LOWER(a.referenceNumber) = LOWER(:referenceNumber)")
    int updateApplicationStatus(@Param("referenceNumber") String reference, @Param("state") StateEnum state, @Param("status") StatusEnum status);

}
