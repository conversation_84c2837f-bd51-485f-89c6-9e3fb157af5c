package bw.org.hrdf.models.login;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Schema(
        name = "AutologinRequest",
        description = "Schema to hold login request details"
)
@Data
public class AutologinRequest {

    private String username;
    private String password;
    private boolean isMobile;
    @Override
    public String toString() {
        return "AutologinRequest [username=" + username + ", password=" + password + "]";
    }

}