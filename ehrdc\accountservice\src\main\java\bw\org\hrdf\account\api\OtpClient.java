package bw.org.hrdf.account.api;

import bw.org.hrdf.account.config.FeignConfig;
import bw.org.hrdf.account.helper.ApiResponse;
import bw.org.hrdf.account.models.otp.OtpRequest;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(name = "otp-service", path = "/api/v1/otp", fallback = OtpClientFallback.class, configuration = FeignConfig.class)
public interface OtpClient {

    @PostMapping("/new")
    ResponseEntity<?> generateOtp(@RequestBody OtpRequest otpRequest);

    @PutMapping("/verify")
    ApiResponse<?> verifyOtp(@RequestBody OtpRequest otpRequest);
}