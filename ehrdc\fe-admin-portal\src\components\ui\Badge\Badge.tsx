import { forwardRef, CSSProperties } from 'react';
import classNames from 'classnames';
import type { CommonProps } from '../@types/common';

// BadgeProps interface
export interface BadgeProps extends CommonProps {
    badgeStyle?: CSSProperties;
    content?: string | number;
    innerClass?: string;
    maxCount?: number;
    status?: string; // Status prop to determine the badge color
}

// Badge component
const Badge = forwardRef<HTMLElement, BadgeProps>((props, ref) => {
    const {
        badgeStyle,
        children,
        className,
        content,
        innerClass,
        maxCount = 99,
        status = 'default', // Use the status prop to determine color
        ...rest
    } = props;

    const dot = typeof content !== 'number' && typeof content !== 'string';
    // Utility function to return a color based on input
    function getColorBasedOnInput(input: string): string {
        switch (input) {
            case 'IN_REVIEW':
                return '#ffc107'; // Yellow
            case 'APPROVED':
                return '#28a745'; // Green
            case 'ACTIVATED':
                return '#007bff'; // Blue
            case 'DEACTIVATED':
                return '#6c757d'; // Gray
            case 'DECLINED':
                return '#dc3545'; // Red
            case 'SENT_BACK_FOR_CORRECTIONS':
                return '#fd7e14'; // Orange
            default:
                return '#6c757d'; // Default gray
        }
    }
    // Get color based on the status
    const color = getColorBasedOnInput(status);

    const badgeClass = classNames(dot ? 'badge-dot' : 'badge', innerClass);

    const renderBadge = () => {
        if (children) {
            return (
                <span
                    ref={ref}
                    className={classNames('badge-wrapper', className)}
                    {...rest}
                >
                    <span
                        className={classNames(badgeClass, 'badge-inner')}
                        style={{ backgroundColor: color, ...badgeStyle }} // Apply the color
                    >
                        {typeof content === 'number' && content > maxCount
                            ? `${maxCount}+`
                            : content}
                    </span>
                    {children}
                </span>
            );
        }
        return (
            <span
                ref={ref}
                className={classNames(badgeClass, className)}
                style={{ backgroundColor: color, ...badgeStyle }} // Apply the color
                {...rest}
            >
                {content}
            </span>
        );
    };

    return renderBadge();
});

Badge.displayName = 'Badge';

export default Badge;
