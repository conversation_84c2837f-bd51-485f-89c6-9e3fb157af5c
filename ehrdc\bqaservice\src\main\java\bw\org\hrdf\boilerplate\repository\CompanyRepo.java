package bw.org.hrdf.boilerplate.repository;

import bw.org.hrdf.boilerplate.entity.Company;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface CompanyRepo extends JpaRepository<Company, Long> {

    @Query("select c from Company c where c.uuid = :uuid ")
    Optional<Company> findByUuid(String uuid);

    @Query("select a from Company a where a.accreditationNo = :accreditationNo")
    Optional<Company> findByAccreditationNo(String accreditationNo);
}
