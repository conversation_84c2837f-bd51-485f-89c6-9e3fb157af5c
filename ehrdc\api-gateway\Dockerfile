# Use the latest official Maven image with JDK 17
FROM maven:latest AS build

# Set the working directory in the container
WORKDIR /app

# Copy the pom.xml and any other necessary files
COPY pom.xml ./
COPY src ./src

# Run Maven to clean and install the project
RUN mvn clean package -DskipTests

# Use a lightweight JRE image for running the application
FROM openjdk:17-slim

# Set the working directory in the container
WORKDIR /app

# Copy the built JAR file from the previous build stage
COPY --from=build /app/target/api-gateway-0.0.1-SNAPSHOT.jar ./api-gateway.jar

# Specify the command to run the application
CMD ["java", "-jar", "api-gateway.jar"]

# Expose the port the app runs on (change as needed)
EXPOSE 8072