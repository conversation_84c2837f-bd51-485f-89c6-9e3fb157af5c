package bw.org.hrdf.boilerplate.services;

import bw.org.hrdf.boilerplate.entity.Company;
import bw.org.hrdf.boilerplate.repository.CompanyRepo;
import bw.org.hrdf.boilerplate.repository.ProgrammesRepo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;
import java.util.Set;

@Service
public class CompanyService {

    @Autowired
    private CompanyRepo companyRepo;

    @Autowired
    private ProgrammesRepo programmesRepo;

    public Company fetchCompanyById(String id) {
        Optional<Company> company = companyRepo.findByUuid(id);
        return company.orElse(null);
    }

    public List<Company> fetchAllCompanies() {
        return companyRepo.findAll();
    }

    public Company fetchAccreditationById(String id) {
        Optional<Company> company = companyRepo.findByAccreditationNo(id);
        return company.orElse(null);
    }


}
