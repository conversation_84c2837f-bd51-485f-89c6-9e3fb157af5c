package bw.org.hrdf.account.repositories.company;

import bw.org.hrdf.account.entity.company.ContactPerson;
import jakarta.transaction.Transactional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface ContactPersonRepository extends JpaRepository<ContactPerson, Long> {
    @Query("SELECT c FROM ContactPerson c WHERE c.uuid = :uuid")
    Optional<ContactPerson> findByUuid(String uuid);

    @Modifying
    @Transactional
    @Query("UPDATE ContactPerson c SET " +
            "c.name = :name, " +
            "c.position = :position, " +
            "c.mobileNumber = :mobileNumber, " +
            "c.email = :email " +
            "WHERE c.uuid = :uuid")
    int updateContact<PERSON>erson(String uuid, String name, String position, String mobileNumber, String email);
}
