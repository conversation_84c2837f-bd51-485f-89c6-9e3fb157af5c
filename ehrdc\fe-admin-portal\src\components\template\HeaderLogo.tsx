import Logo from '@/components/template/Logo'
import { LOGO_X_GUTTER, SIDE_NAV_CONTENT_GUTTER } from '@/constants/theme.constant'
import { RootState } from "@/store";
import { useSelector } from 'react-redux'
const HeaderLogo = () => {
    const sideNavCollapse = useSelector((state: RootState) => state.theme.layout.sideNavCollapse)
    return(
        <>
            <Logo
            /*    mode={logoMode()}*/
                type={sideNavCollapse ? 'streamline' : 'full'}
                className={
                    sideNavCollapse
                        ? SIDE_NAV_CONTENT_GUTTER
                        : LOGO_X_GUTTER
                }
                imgClass={'h-16'}
            />
        </>
        )
}
export default HeaderLogo
