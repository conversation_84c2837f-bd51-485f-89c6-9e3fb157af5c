package bw.org.hrdf.account.models.user;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

@Schema(
        name = "User Model",
        description = "Schema to hold User details"
)
@Data
@NoArgsConstructor @ToString @AllArgsConstructor @Builder
public class UserRequest {
    private String userId;
    private String username;
    private String companyId;
    private String firstName;
    private String lastName;
    private String companyNumber;
}
