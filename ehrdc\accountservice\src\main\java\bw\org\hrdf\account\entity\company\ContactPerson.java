package bw.org.hrdf.account.entity.company;

import bw.org.hrdf.account.entity.Base;
import jakarta.persistence.*;
import lombok.*;

import java.io.Serializable;

/**
 * eHRDF Contact Object representation
 * <AUTHOR>
 *
 */

@Entity
@Getter @Setter @AllArgsConstructor @NoArgsConstructor
@Table(name = "contact_persons")
public class Contact<PERSON>erson extends Base implements Serializable {

	@Column(name = "names", nullable = false)
	private String name;

	@Column(name = "position", nullable = false)
	private String position;
	@Column(name = "mobile_number")
	private String mobileNumber;
	@Column(name = "email")
	private String email;

	@OneToOne
	@JoinColumn(name = "company_id", nullable = false)
	@ToString.Exclude
	private CompanyEntity company;
}