package bw.org.hrdf.entity;

import bw.org.hrdf.entity.enums.PrefsMethod;
import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "user_settings")
@Data
public class UserSettings extends Base{

    @Column(nullable = false, unique = true)
    private String userId;

    @Column()
    private boolean is2FAEnabled;

    @Column
    private PrefsMethod preferred2FAMethod;

    @Column
    private String phoneNumber;

    @Column
    private String backupEmail;

    @Column
    private LocalDateTime lastUpdated;

    // Additional user preferences
    @Column
    private String themePreference = "Light"; // e.g., "Light", "Dark"

    @Column
    private String languagePreference = "en";
}

