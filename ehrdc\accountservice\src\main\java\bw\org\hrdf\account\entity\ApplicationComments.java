package bw.org.hrdf.account.entity;

import bw.org.hrdf.account.entity.company.ApplicationEntity;
import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.persistence.*;
import lombok.*;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @CreatedOn 21/03/25 15:46
 * @UpdatedBy martinspectre
 * @UpdatedOn 21/03/25 15:46
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "application_comments")
@Getter @Setter
public class ApplicationComments extends Base implements Serializable {
    @Column(nullable = false)
    private String action;  // e.g., "APPROVED", "REJECTED", "ASSIGNED"

    @Column(length = 5000)
    private String comments;

    @Column(nullable = false)
    private String updatedBy;

    @Column(nullable = false)
    private String updateRole;

    @Column(nullable = false)
    private LocalDateTime timestamp = LocalDateTime.now();

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "application_id", nullable = false)
    @JsonIgnore
    @ToString.Exclude
    private ApplicationEntity application;
}
