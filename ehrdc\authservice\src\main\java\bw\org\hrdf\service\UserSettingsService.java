package bw.org.hrdf.service;

import bw.org.hrdf.entity.UserLoginPolicies;
import bw.org.hrdf.entity.UserSettings;
import bw.org.hrdf.entity.enums.PrefsMethod;
import bw.org.hrdf.models.common.PolicyModel;
import bw.org.hrdf.repositories.LoginPolicyRepository;
import bw.org.hrdf.repositories.UserSettingsRepository;
import bw.org.hrdf.utils.LoginPolicies;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.Optional;

@Service
public class UserSettingsService {

    @Autowired
    private UserSettingsRepository userSettingsRepository;

    @Autowired
    private LoginPolicyRepository loginPolicyRepository;

    @Autowired
    private LoginPolicies policies;

    public UserLoginPolicies isAccountLocked(String username) {
        Optional<UserLoginPolicies> userLoginPolicies = loginPolicyRepository.findByUser(username);
        return userLoginPolicies.orElse(null);
    }
    public boolean is2FAEnabled(String userId) {
        Boolean response = userSettingsRepository.is2FAEnabled(userId);
        return response != null && response;
    }
    public boolean isFirstTimeLogin(String username) {
        Optional<UserLoginPolicies> userLoginPolicies = loginPolicyRepository.findByUser(username);
        return userLoginPolicies.map(UserLoginPolicies::isFirstTimeLogin).orElse(false);
    }

    @Transactional
    public void updateFirstTimeLogin(String username) {
        Optional<UserLoginPolicies> userLoginPolicies = loginPolicyRepository.findByUser(username);
        if(userLoginPolicies.isPresent()){
            UserLoginPolicies loginPolicies = userLoginPolicies.get();
            loginPolicies.setFirstTimeLogin(false);
            loginPolicies.setUpdatedAt(LocalDateTime.now());

            loginPolicyRepository.save(loginPolicies);
        }
    }


    public UserSettings getUserSettings(String userId) {
        return userSettingsRepository.findByUserId(userId)
                .orElseThrow(() -> new RuntimeException("Settings not found for user ID: " + userId));
    }

    @Transactional
    public UserSettings update2FASettings(String userId, boolean isEnabled) {
        UserSettings settings = userSettingsRepository.findByUserId(userId)
                .orElse(new UserSettings()); // Create new if not exists

        settings.setUserId(userId);
        settings.set2FAEnabled(isEnabled);
        settings.setLastUpdated(LocalDateTime.now());

        return userSettingsRepository.save(settings);
    }

    @Transactional
    public UserSettings updateUserSettings(String userId, PrefsMethod method, String phoneNumber, String backupEmail) {
        UserSettings settings = userSettingsRepository.findByUserId(userId)
                .orElse(new UserSettings()); // Create new if not exists

        settings.setUserId(userId);
        settings.setPreferred2FAMethod(method);
        settings.setPhoneNumber(phoneNumber);
        settings.setBackupEmail(backupEmail);
        settings.setLastUpdated(LocalDateTime.now());

        return userSettingsRepository.save(settings);
    }



    public void updateLoginAttempts(String username, boolean isLoginFailed) {
        Optional<UserLoginPolicies> userLoginPolicies = loginPolicyRepository.findByUser(username);
        UserLoginPolicies newUserLoginPolicies = new UserLoginPolicies();
        if(userLoginPolicies.isPresent()){
            newUserLoginPolicies = userLoginPolicies.get();
            if(isLoginFailed){
                LocalDateTime now = LocalDateTime.now();
                int failedAttempts = newUserLoginPolicies.getFailedAttempts();
                failedAttempts++;
                LocalDateTime lastUpdatedAt = newUserLoginPolicies.getUpdatedAt();

                PolicyModel policy = policies.getLoginPolicies();
                Integer maxRequest = policy.getMaxRequest();
                Integer windowFrame = policy.getWindowFrame();

                if(failedAttempts >= maxRequest){
                    Duration duration = Duration.between(lastUpdatedAt, now);
                    if (duration.getSeconds() > windowFrame) {
                        newUserLoginPolicies.setFailedAttempts(1);
                        newUserLoginPolicies.setAccountLocked(false);
                    }else{
                        newUserLoginPolicies.setAccountLocked(true);
                    }
                }else{
                    newUserLoginPolicies.setFailedAttempts(failedAttempts);
                }
            }else{
                newUserLoginPolicies.setAccountLocked(false);
                newUserLoginPolicies.setFailedAttempts(0);
                newUserLoginPolicies.setFirstTimeLogin(false);
            }
        }else{
            newUserLoginPolicies.setFirstTimeLogin(true);
            newUserLoginPolicies.setUsername(username);
            newUserLoginPolicies.setFailedAttempts(1);
            newUserLoginPolicies.setAccountLocked(false);
        }
        newUserLoginPolicies.setUpdatedAt(LocalDateTime.now());

        loginPolicyRepository.save(newUserLoginPolicies);
    }

    public int updateExpiredLoginAttempts() {
        return loginPolicyRepository.updateExpiredLoginAttempts();
    }
}
