package bw.org.hrdf.account.service;

import bw.org.hrdf.account.entity.ApplicationComments;
import bw.org.hrdf.account.repositories.ApplicationCommentsRepo;
import jakarta.transaction.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @CreatedOn 21/03/25 15:51
 * @UpdatedBy martinspectre
 * @UpdatedOn 21/03/25 15:51
 */
@Service
public class ApplicationCommentsService {
    @Autowired
    private ApplicationCommentsRepo commentsRepo;

    public List<ApplicationComments> getApplicationComments(String applicationId) {
        return commentsRepo.findByCommentsApplicationId(applicationId);
    }

    @Transactional
    public void createComments(ApplicationComments comments) {
        commentsRepo.save(comments);
    }
}
