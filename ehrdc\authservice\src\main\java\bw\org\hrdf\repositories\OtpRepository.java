package bw.org.hrdf.repositories;

import bw.org.hrdf.entity.OtpEntity;
import bw.org.hrdf.entity.enums.OTPTypes;
import bw.org.hrdf.entity.enums.OtpStatus;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Optional;
import java.util.UUID;

@Repository
public interface OtpRepository extends JpaRepository<OtpEntity, Integer> {

    @Query("SELECT o FROM OtpEntity o WHERE o.otp = :otp")
    Optional<OtpEntity> findByOtp(Integer otp);

    @Query("SELECT o FROM OtpEntity o WHERE o.reference = :userId and o.otp= :otp and o.type= :type")
    Optional<OtpEntity> findByUserAndOtp(String userId, Integer otp,  OTPTypes type);

    @Query("SELECT o FROM OtpEntity o WHERE o.reference = :userId and o.type= :type")
    Optional<OtpEntity> findByUserAndType(String userId, OTPTypes type);

    @Transactional
    @Modifying
    @Query("DELETE FROM OtpEntity o WHERE o.uuid = :uuid")
    void deleteByUuid(UUID uuid);

    @Transactional
    @Modifying
    @Query("UPDATE OtpEntity o SET o.status = :status WHERE o.lifetime < :currentTime AND o.status = 'ACTIVE'")
    int invalidateExpiredTokens(OtpStatus status, LocalDateTime currentTime);
}
