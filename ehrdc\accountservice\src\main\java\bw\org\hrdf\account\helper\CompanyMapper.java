package bw.org.hrdf.account.helper;

import bw.org.hrdf.account.dto.*;
import bw.org.hrdf.account.entity.company.*;
import bw.org.hrdf.account.entity.enums.Gender;

import java.util.Collections;
import java.util.Set;
import java.util.stream.Collectors;

public class CompanyMapper {

    public static CompanyDTO toDTO(CompanyEntity entity) {
        if (entity == null) return null;

        // Perform null checks for each property
        String uuid = entity.getUuid();
        String name = entity.getName();
        String type = entity.getType() != null ? entity.getType().name() : null;
        String industry = entity.getIndustry();
        String category = entity.getCategory() != null ? entity.getCategory().name() : null;
        boolean pepPipStatus = entity.isPepPipStatus();
        boolean pepPipAssociateStatus = entity.isPepPipAssociateStatus();
        String physicalAddress = entity.getPhysicalAddress();
        String telephoneNumber = entity.getTelephoneNumber();
        String faxNumber = entity.getFaxNumber();

        ContactPersonDTO contactPersonDTO = toContactPersonDTO(entity.getContactPerson());
        Set<EmployeeDTO> employees = entity.getEmployees() != null
                ? entity.getEmployees().stream().map(CompanyMapper::toEmployeeDTO).collect(Collectors.toSet())
                : Collections.emptySet();
        ApplicationDTO registrationApplicationDTO = toApplicationDTO(entity.getRegistrationApplication());
        Set<VerificationDTO> verifications = entity.getVerifications() != null
                ? entity.getVerifications().stream().map(CompanyMapper::toVerificationDTO).collect(Collectors.toSet())
                : Collections.emptySet();

        return new CompanyDTO(
                uuid,
                name,
                type,
                industry,
                category,
                physicalAddress,
                telephoneNumber,
                faxNumber,
                pepPipStatus,
                pepPipAssociateStatus,
                contactPersonDTO,
                employees,
                registrationApplicationDTO,
                verifications
        );
    }

    public static ContactPersonDTO toContactPersonDTO(ContactPerson entity) {
        if (entity == null) return null;

        return new ContactPersonDTO(
                entity.getId(),
                entity.getUuid(),
                entity.getName(),
                entity.getPosition(),
                entity.getMobileNumber(),
                entity.getEmail()
        );
    }

    public static EmployeeDTO toEmployeeDTO(Employee entity) {
        if (entity == null) return null;
        EmployeeDTO employee = new EmployeeDTO();
        employee.setUuid(entity.getUuid());
        employee.setFirstName(entity.getFirstName());
        employee.setLastName(entity.getLastName());
        employee.setIdNumber(entity.getIdNumber());
        employee.setIdType(entity.getIdType().name());
        employee.setIdVerificationStatus(entity.getIdVerificationStatus().name());
        employee.setGender(entity.getGender());
        employee.setContactNumber(entity.getContactNumber());
        employee.setBasicSalary(entity.getBasicSalary());

        return employee;
    }

    public static ApplicationDTO toApplicationDTO(ApplicationEntity entity) {
        if (entity == null) return null;

        return new ApplicationDTO(
                entity.getUuid(),
                entity.getReferenceNumber(),
                entity.getStatus().name(),
                entity.getState().name(),
                entity.getApplicationSubmissionDate(),
                entity.getActionsRequested().stream().map(CompanyMapper::toActionDTO).collect(Collectors.toList()),
                entity.getAgentLead(), entity.getAgent(), entity.getManager()
        );
    }

    public static ActionDTO toActionDTO(Actions entity) {
        if (entity == null) return null;

        return new ActionDTO(
                entity.getUuid(),
                entity.getType().name(),
                entity.getMessage(),
                entity.getStatus().name()
        );
    }

    public static VerificationDTO toVerificationDTO(Verification entity) {
        if (entity == null) return null;

        return new VerificationDTO(
                entity.getUuid(),
                entity.getReference(),
                entity.getType().name(),
                entity.getStatus().name(),
                entity.getVerifiedAt(),
                entity.getExpiryDate()
        );
    }
}

