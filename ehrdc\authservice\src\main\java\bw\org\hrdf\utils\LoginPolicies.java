package bw.org.hrdf.utils;

import bw.org.hrdf.models.common.PolicyModel;
import lombok.Data;
import org.springframework.context.annotation.Configuration;

@Configuration
@Data
public class LoginPolicies {

    public PolicyModel getLoginPolicies(){
        PolicyModel policies = new PolicyModel();
        policies.setMaxRequest(3);
        policies.setWindowFrame(120);
        return policies;
    }

    public PolicyModel getDefaultPolicies(){
        PolicyModel policies = new PolicyModel();
        policies.setMaxRequest(5);
        policies.setWindowFrame(120);
        return policies;
    }
}
