package bw.org.hrdf.account.dto;

import bw.org.hrdf.account.entity.enums.Gender;
import bw.org.hrdf.account.entity.enums.IDType;
import bw.org.hrdf.account.entity.enums.VerificationStatusEnum;
import jakarta.persistence.Column;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class EmployeeDTO {
    private String uuid;
    private String firstName;
    private String lastName;
    private String idNumber;
    private String idType;
    private String idVerificationStatus;
    private Gender gender;
    private String contactNumber;
    private String basicSalary;
}

