package bw.org.hrdf.api;

import bw.org.hrdf.config.FeignConfig;
import bw.org.hrdf.helper.ApiResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

@FeignClient(name = "account-service", path = "/api/v1/registration/user", fallback = CompanyClientFallback.class, configuration = FeignConfig.class)
public interface CompanyClient {
    @GetMapping("/{userId}/assigned-company")
    ApiResponse<?> fetchCompanyIdentifier(@RequestParam String userId);
}