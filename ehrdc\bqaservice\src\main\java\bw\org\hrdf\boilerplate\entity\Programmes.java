package bw.org.hrdf.boilerplate.entity;

import com.fasterxml.jackson.annotation.JsonBackReference;
import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.Date;

@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "mst_programmes")
@Data
public class Programmes extends Base{

    @Column(name="programme_name", nullable = false)
    private String programmeName;

    @Column(name="number_of_learners", nullable = false)
    private Integer noOfLearners;

    @Column(name="course_type", nullable = false)
    private CourseTypesEnum courseType;

    @Column(name="awarding_board", nullable = false)
    private String awardingBoard;

    @Column(name="date_of_accreditation", nullable = false)
    private LocalDateTime dateOfAccreditation;

    @Column(name="expiry_date", nullable = false)
    private Date expiryDate;

    @Column(name="module", nullable = false)
    private String module;

    @ManyToOne(targetEntity = Company.class, cascade = CascadeType.ALL)
    @JoinColumn(name = "company_reference")
    @JsonBackReference
    private Company company;

}