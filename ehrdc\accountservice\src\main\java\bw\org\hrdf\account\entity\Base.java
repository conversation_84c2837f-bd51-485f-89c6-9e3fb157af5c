package bw.org.hrdf.account.entity;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

import jakarta.persistence.*;
import lombok.Data;

import java.util.UUID;

/**
 * eHRDF Base Object representation
 * <AUTHOR>
 *
 */

@MappedSuperclass
@JsonIgnoreProperties(ignoreUnknown=true)
@Data
public abstract class Base {

	@JsonProperty(value="Id")
	@Column(nullable = false, updatable = false)
	private Long id;

	@Id
	@Column(nullable = false, updatable = false, unique = true)
	@JsonProperty(value="Uuid")
	private String uuid = UUID.randomUUID().toString();

	@PrePersist
	protected void onCreate() {
		this.id = System.nanoTime();
	}
}