export type SignInResponse = {
    status:boolean
    message:string,
    data:{
        accessToken:string,
        idToken?:string,
        refreshToken:string,
        expiresIn:number,
        userId:string,
        roles :{
            realmRoles:string[]
            roles:string[]
        },
        organisationId?:string,
        otpRequired?:boolean,
        otpType?: null,
        otpLifetime?: number,
        nextStep?:unknown
    },
    errors?:[
        {
            errorCode:string,
            errorMessage:string
        }
    ]
    /* token: string
     user: {
         userName: string
         authority: string[]
         avatar: string
         email: string
     }*/
}
export type SignInCredential = {
    username: string
    password: string
}
export type SignUpCredential = {
    email: string
    firstname: string
    lastname: string
}
export type SignUpResponse ={
    status:boolean,
    message:string
    data?:{
        UserId:string
    },
    errors?:[
        {
            errorCode:string,
            errorMessage:string
        }
    ]
}
export type SignOutCredential = {
    userId: string
}
export type SignOutResponse ={
    status:boolean,
    message:string
    data?:{
        UserId?:string
    },
    errors?:[
        {
            errorCode:string,
            errorMessage:string
        }
    ]
}
export type CompanySignUpCredential = {
    name: string,
    cipaRegistrationNumber: string
    physicalAddress: string
    contactPerson: string
    contactPersonPosition: string
    email: string
    faxNumber: string
    telephoneNumber: string
    mobileNumber: string
    bqaAccreditationExpiry?: string
    bqaAccreditationNumber?: string
    tinNumber?: string
    sector: string
    type: string
    category: string
    applicationStatus: string
}

export type ForgotPassword = {
    email: string
}

export type ResetPassword = {
    password: string
}

export type FetchETPs = {
    content: [object]
}
