package bw.org.hrdf.account.service;

import bw.org.hrdf.account.dto.BackOfficeProfile;
import bw.org.hrdf.account.models.otp.OtpRequest;
import org.keycloak.admin.client.Keycloak;
import org.keycloak.admin.client.resource.ClientResource;
import org.keycloak.admin.client.resource.RealmResource;
import org.keycloak.admin.client.resource.RoleResource;
import org.keycloak.admin.client.resource.UserResource;
import org.keycloak.representations.idm.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.*;
import java.util.stream.Collectors;

/**
 * eHRDF Account Service
 * This service provides service to create, retrieve
 * update & delete account object in eHRDF.
 *
 * <AUTHOR>
 *
 */
@RefreshScope
@Service
public class BackOfficeUsersService {

    @Autowired
    private Keycloak keycloak;

    @Value("${keycloak.realm}")
    private String realm;

    @Value("${keycloak.client-id}")
    private String clientId;

    @Value("${keycloak.client-secret}")
    private String clientSecret;

    @Autowired
    private KafkaTemplate<String, OtpRequest> kafkaTemplate;

    @Autowired
    private RestTemplate restTemplate;

    private static final Logger logger = LoggerFactory.getLogger(BackOfficeUsersService.class);

    public List<BackOfficeProfile> retrieveUsersByRoles(String roleName){
        RealmResource realmResource = keycloak.realm(realm);

        Optional<ClientResource> clientResource = realmResource.clients().findAll().stream()
                .filter(client -> client.getClientId().equals(clientId))
                .findFirst()
                .map(client -> realmResource.clients().get(client.getId()));

        if (clientResource.isPresent()) {
            RoleResource roleResource = clientResource.get().roles().get(roleName);


            return roleResource.getUserMembers().stream()
                    .map(user -> new BackOfficeProfile(
                            user.getId(),
                            user.getEmail(),
                            user.isEnabled(),
                            user.getFirstName(),
                            user.getLastName(),
                            user.getUsername()
                    ))
                    .toList();
        } else {
            throw new RuntimeException("Client with ID " + clientId + " not found");
        }
    }

    public List<BackOfficeProfile> retrieveUsersByRoleAndGroup(String roleName, String group) {
        RealmResource realmResource = keycloak.realm(realm);

        Optional<ClientResource> clientResource = realmResource.clients().findAll().stream()
                .filter(client -> client.getClientId().equals(clientId))
                .findFirst()
                .map(client -> realmResource.clients().get(client.getId()));

        if (clientResource.isEmpty()) {
            throw new RuntimeException("Client with ID " + clientId + " not found");
        }

        RoleResource roleResource = clientResource.get().roles().get(roleName.toLowerCase());
        List<UserRepresentation> users = roleResource.getUserMembers();

        if (group != null) {
            // Filter users by group
            List<UserRepresentation> filteredUsers = users.stream()
                    .filter(user -> userBelongsToGroup(user, group.toLowerCase(), realmResource))
                    .toList();

            return mapToBackOfficeProfiles(filteredUsers);
        }

        return mapToBackOfficeProfiles(users);
    }

    private boolean userBelongsToGroup(UserRepresentation user, String group, RealmResource realmResource) {
        List<GroupRepresentation> userGroups = realmResource.users().get(user.getId()).groups();
        return userGroups.stream().anyMatch(g -> g.getName().equalsIgnoreCase(group));
    }

    private List<BackOfficeProfile> mapToBackOfficeProfiles(List<UserRepresentation> users) {
        return users.stream()
                .map(user -> new BackOfficeProfile(
                        user.getId(),
                        user.getEmail(),
                        user.isEnabled(),
                        user.getFirstName(),
                        user.getLastName(),
                        user.getUsername()
                ))
                .toList();
    }

}

