import { ActionTypes as actionTypes } from './actionTypes'
import { Filter } from '@/views/modules/complaints/types'

// Generic Action Interface
interface Action<T = any> {
    type: string;
    payload?: T;
}



//Fetch recognition applications
const getComplaints = (pageNumber: number, size: number, filter: Filter): Action => ({
    type: actionTypes.FETCH_COMPLAINTS_REQUESTS,
    payload: { pageNumber, size, filter }
})

// Exported Actions and Events
export const actions = {
    getComplaints
}

export const events = {
    fetch: {
        REQUEST: actionTypes.FETCH_COMPLAINTS_REQUESTS,
        RECEIVED: actionTypes.FETCH_COMPLAINTS_REQUESTS_SUCCESS,
        FAILED: actionTypes.FETCH_COMPLAINTS_REQUESTS_FAILED
    },
    comment: {
        REQUEST: actionTypes.SUBMIT_COMPLAINT_COMMENT_REQUEST,
        RECEIVED: actionTypes.SUBMIT_COMPLAINT_COMMENT_SUCCESS,
        FAILED: actionTypes.SUBMIT_COMPLAINT_COMMENT_FAILED
    },
    upload: {
        REQUEST: actionTypes.UPLOAD_COMPLAINT_ATTACHMENT_REQUEST,
        RECEIVED: actionTypes.UPLOAD_COMPLAINT_ATTACHMENT_SUCCESS,
        FAILED: actionTypes.UPLOAD_COMPLAINT_ATTACHMENT_FAILED
    }
}
