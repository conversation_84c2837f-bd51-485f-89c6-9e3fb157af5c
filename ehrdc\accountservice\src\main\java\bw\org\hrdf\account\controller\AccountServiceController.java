package bw.org.hrdf.account.controller;

import bw.org.hrdf.account.entity.enums.CommunicationType;
import bw.org.hrdf.account.entity.enums.OTPTypes;
import bw.org.hrdf.account.helper.ApiResponse;
import bw.org.hrdf.account.models.authentication.AccountActivationResponse;
import bw.org.hrdf.account.models.otp.OtpRequest;
import bw.org.hrdf.account.models.authentication.SignUpResponse;
import bw.org.hrdf.account.models.user.UserRequest;
import bw.org.hrdf.account.service.AccountService;
import jakarta.validation.Valid;
import org.keycloak.representations.idm.UserRepresentation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.*;
import org.springframework.web.bind.annotation.*;

import java.util.*;

import static bw.org.hrdf.account.helper.ApiResponse.getInternalServerError;

/**
 * eHRDF Account Service Controller
 * This controller exposes URI to create, retrieve
 * update & delete account object in eHRDF.
 *
 * <AUTHOR> Ntlhe
 *
 */

@RestController
@RequestMapping("/api/v1/registration/user")
public class AccountServiceController {

    private static final Logger logger = LoggerFactory.getLogger(AccountServiceController.class);

    @Autowired
    private AccountService accountService;

    @PostMapping("/new")
    public ResponseEntity<?> registerUser(@Valid @RequestBody UserRequest userRequest) {
        logger.info("Account create initiated by: {}", userRequest.getUsername());
        try {
            UserRepresentation userDetails = accountService.retrieveUserByUsername(userRequest.getUsername());
            if (userDetails != null) {
                logger.error("Account create for {} failed, user exist", userRequest.getUsername());
                return ApiResponse.createErrorResponse("EMAIL_ID_ALREADY_REGISTERED", "Provided email/phone number exist");
            }

            List<String> realmRoles = List.of();
            List<String> clientRoles = Arrays.asList("user-admin", "user");
            String userId = accountService.createUser(
                    userRequest,
                    realmRoles,
                    clientRoles
            );
            logger.info("Account for user: {} is created successfully, ", userRequest.getUsername());

            if(userRequest.getCompanyNumber() != null && !userRequest.getCompanyNumber().isEmpty()){
                logger.info("Company of cipa number {} validation initiated", userRequest.getCompanyNumber());
                //Trigger company verification from cipa and HRDC database
                /**
                 * If Company exist from cipa trigger HRDC company validation
                 * If exist from HRDC databse, pull company details and create company application
                 */
            }

            OtpRequest otpRequest = new OtpRequest();
            otpRequest.setOtpType(OTPTypes.ACCOUNT_VERIFICATION);
            otpRequest.setUserId(userId);
            otpRequest.setContactAddress(userRequest.getUsername());
            otpRequest.setCommunicationType(CommunicationType.EMAIL);
            accountService.triggerOtp(otpRequest);

            SignUpResponse userSignUpResponse = new SignUpResponse();
            userSignUpResponse.setUserId(userId);
            userSignUpResponse.setUsername(userRequest.getUsername());
            logger.info("Account created successfully for user: {}", userRequest.getUsername());

            return ResponseEntity.ok(new ApiResponse<>(true, "Account created successfully", userSignUpResponse, null));
        } catch (Exception e) {
            logger.error("Account sign up failed with exception: {}", e.getMessage());
            return getInternalServerError(e.getMessage());
        }
    }

    @PutMapping("/{userId}/verify")
    public ResponseEntity<?> verifyUserAccount(@PathVariable String userId, @Valid @RequestBody OtpRequest otpRequest) {
        try {
            logger.info("Account activation initiated by: {}", userId);
            UserRepresentation userDetails = accountService.retrieveUserByUserId(userId);
            if (userDetails != null) {
                OTPTypes otpType = otpRequest.getOtpType();
                Integer otpNumber = otpRequest.getOtp();
                String activationResponse = accountService.activateAccount( userDetails, otpType, otpNumber);
                AccountActivationResponse response = new AccountActivationResponse();
                response.setCanFinishJourney(false);
                if(activationResponse.equals("ACCOUNT_VERIFIED_SUCCESSFULLY")){
                    logger.info("Account activation is successful for the user: {}", userId);
                    response.setCanFinishJourney(true);
                    return ResponseEntity.ok(
                            new ApiResponse<>(true, "Account activated successfully", response, null)
                    );
                }
                logger.warn("Account activation failed for user: {}", userId);
                return ApiResponse.createErrorResponse(activationResponse, "Account activation failed");
            }
            logger.warn("Account activation failed with error: {}", "User " + userId + " is not found");
            return ApiResponse.createErrorResponse("USER_NOT_FOUND", "Provided user id does not exist");
        } catch (Exception e) {
            String message = e.getMessage();
            String errorCode = "INTERNAL_SERVER_ERROR";
            String errorMessage = "Internal error occurred";
            HttpStatus status = HttpStatus.INTERNAL_SERVER_ERROR;

            if(e.getMessage().equals("OTP_EXPIRED")){
                message = "Provided Otp number " + otpRequest.getOtp() + " for " + userId + " is expired";
                errorCode = "OTP_EXPIRED";
                errorMessage = "Otp number is expired";
                status = HttpStatus.OK;
            }else if(e.getMessage().equals("INVALID_OTP_NUMBER")){
                message = "Provided Otp number " + otpRequest.getOtp() + " for " + userId + " is not correct/not found";
                errorCode = "INVALID_OTP_NUMBER";
                errorMessage = "Otp number is incorrect";
                status = HttpStatus.OK;
            }else if(e.getMessage().equals("INVALID_OTP_TYPE")){
                message = "Provided Otp type " + otpRequest.getOtpType() + " for " + userId + " is not valid for account activation -> expected type is ACCOUNT_VERIFICATION";
                errorCode = "INVALID_OTP_TYPE";
                errorMessage = "Otp type is wrong for account activation";
                status = HttpStatus.OK;
            }
            logger.error("Account activation failed with exception: {}", message);
            return ResponseEntity.status(status)
                    .body(new ApiResponse<>(false, errorMessage, null, List.of(
                            new ApiResponse.ErrorResponse(errorCode, errorMessage)
                    )));
        }
    }

    @PostMapping("/otp-resend")
    public ResponseEntity<?> otpResend(@RequestBody OtpRequest otpRequest) {
        logger.info("Otp resend initiated by: {}", (otpRequest.getUserId() != null && !otpRequest.getUserId().isEmpty()) ? otpRequest.getUserId() : otpRequest.getContactAddress());
        try {
            UserRepresentation userDetails = null;
            if(otpRequest.getUserId() != null && !otpRequest.getUserId().isEmpty()){
                userDetails = accountService.retrieveUserByUserId(otpRequest.getUserId());
            } else if (otpRequest.getContactAddress()  != null && !otpRequest.getContactAddress().isEmpty()) {
                userDetails = accountService.retrieveUserByUsername(otpRequest.getContactAddress());
            }
            if (userDetails != null) {
                otpRequest.setContactAddress(userDetails.getUsername());
                otpRequest.setCommunicationType(CommunicationType.EMAIL);
                accountService.triggerOtp(otpRequest);

                logger.info("Otp resend request for user: {} is successful", otpRequest.getUserId());

                return ResponseEntity.ok(new ApiResponse<>(true, "Otp resend successfully", null, null));
            }
            logger.warn("Otp resend failed because user: {} does not exist/was not found in our application", otpRequest.getUserId());

            return ApiResponse.createErrorResponse("USER_NOT_FOUND", "Provided user id does not exist");
        } catch (Exception e) {
            logger.error("Otp resend failed with exception: {}", e.getMessage());
            return getInternalServerError(e.getMessage());
        }
    }

    @GetMapping("/{userId}/assigned-company")
    public ResponseEntity<ApiResponse<?>> getUserCompanyId(@PathVariable String userId) {
        try {
            String companyId = accountService.retrieveCompanyIdByUserId(userId);
            if(companyId.isEmpty()){
                return ResponseEntity.status(HttpStatus.OK)
                        .body(new ApiResponse<>(false, "No record found", null, null));
            }
            return ResponseEntity.ok(new ApiResponse<>(true, "Record found", companyId, null));
        } catch (Exception e) {
            return getInternalServerError(e.getMessage());
        }
    }

}