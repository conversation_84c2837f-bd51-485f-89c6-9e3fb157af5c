package bw.org.hrdf.account.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ApplicationDTO {
    private String uuid;
    private String referenceNumber;
    private String status;
    private String state;
    private LocalDateTime applicationSubmissionDate;
    private List<ActionDTO> actionsRequested;
    private String agentLead;
    private String agent;
    private String manager;
}

