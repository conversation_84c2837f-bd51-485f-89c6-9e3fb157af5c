package bw.org.hrdf.account.models.company;

import bw.org.hrdf.account.entity.enums.VerificationStatusEnum;
import bw.org.hrdf.account.entity.enums.VerificationTypeEnum;
import lombok.*;

import java.time.LocalDateTime;
import java.util.Date;

@Data
@NoArgsConstructor @ToString @AllArgsConstructor @Builder
public class VerificationModel {
    private String name;
    private String reference;
    private VerificationTypeEnum type;
    private VerificationStatusEnum status;
    private LocalDateTime verifiedAt;
    private Date expiryDate;
    private String companyId;
}
