package bw.org.hrdf.api;

import bw.org.hrdf.config.FeignConfig;
import bw.org.hrdf.helper.ApiResponse;
import bw.org.hrdf.models.otp.OtpRequest;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(name = "otp-service", path = "/api/v1/otp", fallback = OtpClientFallback.class, configuration = FeignConfig.class)
public interface OtpClient {

    @PostMapping("/new")
    ApiResponse<?> generateOtp(@RequestBody OtpRequest otpRequest);

    @PutMapping("/verify")
    ApiResponse<?> verifyOtp(@RequestBody OtpRequest otpRequest);
}