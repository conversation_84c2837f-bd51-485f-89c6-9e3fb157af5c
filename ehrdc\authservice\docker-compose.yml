version: '3.8'

services:
  app:
    image: weblogic/authservice:${IMAGE_TAG}
    container_name: ${CONTAINER_NAME}
    ports:
      - "3452:3452"
    volumes:
      - authservice:/data_storage
    networks:
        - security
    restart: always
    environment:
      SPRING_PROFILES_ACTIVE: ${PROFILE_ACTIVE}
      DB_HOST: ${DB_HOST}
      DB_PORT: ${DB_PORT}
      DB_NAME: ${DB_NAME}
      DB_USERNAME: ${DB_USERNAME}
      DB_PASSWORD: ${DB_PASSWORD}
      KC_CLIENT_ID: ${KC_CLIENT_ID}
      KC_CLIENT_SECRET: ${KC_CLIENT_SECRET}
      KC_ISSUER_URI: ${KC_ISSUER_URI}
      KC_REALM: ${KC_REALM}
      KC_ADMIN_USERNAME: ${KC_ADMIN_USERNAME}
      KC_ADMIN_PASSWORD: ${KC_ADMIN_PASSWORD}
      EUREKA_DEFAULT_ZONE: ${EUREKA_DEFAULT_ZONE}

    logging:
      options:
        max-size: "10m"
        max-file: "3"

networks:
  security:
    external: true
volumes:
  authservice:
