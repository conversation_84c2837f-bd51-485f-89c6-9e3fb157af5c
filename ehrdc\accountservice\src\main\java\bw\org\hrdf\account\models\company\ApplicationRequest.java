package bw.org.hrdf.account.models.company;

import bw.org.hrdf.account.entity.enums.StateEnum;
import bw.org.hrdf.account.entity.enums.StatusEnum;
import jakarta.persistence.Column;
import lombok.*;
import java.time.LocalDateTime;

@Data
@NoArgsConstructor @ToString @AllArgsConstructor @Builder
public class ApplicationRequest {
    private String referenceNumber;
    private StatusEnum status;
    private StateEnum state;
    private String companyId;
    private LocalDateTime applicationSubmissionDate;
    private String agentLead = null;
    private String agent = null;
    private String manager = null;
}
