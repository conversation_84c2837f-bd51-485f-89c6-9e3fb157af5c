import Avatar from '@/components/ui/Avatar'
import Dropdown from '@/components/ui/Dropdown'
import withHeaderItem from '@/utils/hoc/withHeaderItem'
import { Link } from 'react-router-dom'
import classNames from 'classnames'
import { HiOutlineLogout, HiOutlineUser } from 'react-icons/hi'
import type { CommonProps } from '@/@types/common'
import { FaUserCircle } from 'react-icons/fa'
import useResponsive from '@/utils/hooks/useResponsive'
import { RootState } from '@/store'
import { useAppSelector } from '@/store/hooks'
import LogoutModal from '../shared/Modals/LogoutModal'
import { useState } from 'react'

type DropdownList = {
    label: string
    path: string
    icon: JSX.Element
}

const dropdownItemList: DropdownList[] = []

const _UserDropdown = ({ className }: CommonProps) => {
    
    const user = useAppSelector((state: RootState) => state?.Auth?.user);

    const {larger}=useResponsive()
    const [openLogOutModal, setOpenLogOutModal] = useState(false);

    const UserAvatar = (
        <div className={classNames(className, 'flex items-center gap-2')}>
            <FaUserCircle size={larger.md ? 32 : 24} color={'#7B809A'}/>
        </div>
    )

    return (
        <div>
            <Dropdown
                menuStyle={{ minWidth: 240 }}
                renderTitle={UserAvatar}
                placement="bottom-end"
            >
                <Dropdown.Item variant="header">
                    <div className="py-2 px-3 flex items-center gap-2">
                        <Avatar shape="circle" icon={<HiOutlineUser  />} />
                        <div>
                            <div className="font-bold text-gray-900 dark:text-gray-100">
                                {`${user?.firstName || ""} ${user?.lastName || ""}`}
                            </div>
                            <div className="text-xs">{`${user?.email || user?.phoneNumber || ""}`}</div>
                        </div>
                    </div>
                </Dropdown.Item>
                <Dropdown.Item variant="divider" />
                {dropdownItemList.map((item) => (
                    <Dropdown.Item
                        key={item.label}
                        eventKey={item.label}
                        className="mb-1 px-0"
                    >
                        <Link 
                            className="flex h-full w-full px-2" 
                            to={item.path}
                        >
                            <span className="flex gap-2 items-center w-full">
                                <span className="text-xl opacity-50">
                                    {item?.icon}
                                </span>
                                <span>{item?.label}</span>
                            </span>
                        </Link>
                    </Dropdown.Item>
                ))}
                <Dropdown.Item
                    eventKey="Sign Out"
                    className="gap-2"
                    onClick={()=>setOpenLogOutModal(true)}
                >
                    <span className="text-xl opacity-50">
                        <HiOutlineLogout />
                    </span>
                    <span>Sign Out</span>
                </Dropdown.Item>
            </Dropdown>
            <LogoutModal open={openLogOutModal} onHandleClose={()=>setOpenLogOutModal(false)}/>
        </div>
    )
}

const UserDropdown = withHeaderItem(_UserDropdown)

export default UserDropdown
