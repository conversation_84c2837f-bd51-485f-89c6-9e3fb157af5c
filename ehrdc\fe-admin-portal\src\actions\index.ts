import { ActionTypes as actionTypes } from "./actionTypes";
import { events as ncbscEvents, actions as ncbscActions } from "../actions/workplace/ncbsc";
import { events as complaintEvents, actions as complaintActions } from "../actions/complaints/";
import { events as appealEvents, actions as appealActions } from "../actions/appeals/";
// Generic Action Interface
interface Action<T = any> {
    type: string;
    payload?: T;
}

const cancelErrorAction = (): Action => ({ type: actionTypes.ERROR_CANCELED });
const completeLoadingAction = (): Action => ({ type: actionTypes.LOADING_COMPLETED });
const requestLoadingAction = (apiType?: string, actionName?: string): Action => ({
    type: actionTypes.LOADING_RECEIVED,
    payload: { apiType, actionName },
});

/** Migrations */
export const setCurrentRouteKey = (payload: string): Action => ({
    type: actionTypes.SET_CURRENT_ROUTE_KEY,
    payload,
});

export const setDirection = (direction: any) => ({
    type: actionTypes.SET_DIRECTION,
    payload: direction,
});

export const setMode = (mode: any) => ({
    type: actionTypes.SET_MODE,
    payload: mode,
});

export const setLayout = (layout: any) => ({
    type: actionTypes.SET_LAYOUT,
    payload: layout,
});

export const setPreviousLayout = (previousLayout: any) => ({
    type: actionTypes.SET_PREVIOUS_LAYOUT,
    payload: previousLayout,
});

export const setSideNavCollapse = (collapse: any) => ({
    type: actionTypes.SET_SIDE_NAV_COLLAPSE,
    payload: collapse,
});

export const setNavMode = (navMode: any) => ({
    type: actionTypes.SET_NAV_MODE,
    payload: navMode,
});

export const setPanelExpand = (expand: any) => ({
    type: actionTypes.SET_PANEL_EXPAND,
    payload: expand,
});

export const setThemeColor = (color: any) => ({
    type: actionTypes.SET_THEME_COLOR,
    payload: color,
});

export const setThemeColorLevel = (colorLevel: any) => ({
    type: actionTypes.SET_THEME_COLOR_LEVEL,
    payload: colorLevel,
});

export const setLang = (lang: any) => ({
    type: actionTypes.SET_LANG,
    payload: lang,
});

const createAccount = (userName: string, companyNumber?: string, isExistingUser: boolean = false): Action => ({
    type: actionTypes.REQUEST_CREATE_ACCOUNT,
    payload: { userName, companyNumber, isExistingUser },
});

const requestOtpAccountActivation = (emailId: string, otp: string, userId?: string): Action => ({
    type: actionTypes.REQUEST_ACCOUNT_ACTIVATION,
    payload: { emailId, otp, userId },
});

const requestLogin = (user: string, password: string, saveUserDataInCache = false, isMobile = false): Action => ({
    type: actionTypes.REQUEST_LOGIN,
    payload: { user, password, saveUserDataInCache, isMobile },
});
const request2faLogin = (user: string, password: string, userId: any, otpNumber: any, saveUserDataInCache = false, isMobile = false): Action => ({
    type: actionTypes.REQUEST_2FA_LOGIN,
    payload: { user, password, userId, otpNumber, saveUserDataInCache, isMobile },
});

const updateAuthToken = (): Action => ({
    type: actionTypes.REQUEST_REFRESH_JWT_TOKEN,
});
const verifyUseranme = (user: string, saveUserDataInCache = false): Action => ({
    type: actionTypes.REQUEST_USERNAME_VERIFY,
    payload: { user, saveUserDataInCache },
});





const destroySession = (): Action => ({
    type: actionTypes.DESTROY_SESSION,
});
const lockSession = (): Action => ({
    type: actionTypes.LOCK_SESSION,
});

const fetchSubordinates = (role: String, group: String) => ({
    type: actionTypes.FETCH_AGENTS ,
    payload: { role, group },
  });





const requestChangePassword = (emailId: string): Action => ({
    type: actionTypes.REQUEST_CHANGE_PASSWORD,
    payload: emailId,
});

const resetPassword = (password: string, otp: string): Action => ({
    type: actionTypes.REQUEST_RESET_CHANGE_PASSWORD,
    payload: { password, otp },
});


// Account Actions
const initiateProfileUpdate = (payload: Record<string, any>): Action => ({
    type: actionTypes.ACCOUNT_UPDATE_REQUEST,
    payload,
});
const registerNewCompany = (userId: string, values: any): Action => ({
    type: actionTypes.REGISTER_COMPANY_REQUEST,
    payload: { userId, values },
});
const fetchCompany = (companyId: string): Action => ({
    type: actionTypes.FETCH_COMPANY_REQUEST,
    payload: { companyId },
});









const otpSend = (userId: string, otpType: string) => ({
    type: actionTypes.OTP_SEND,
    userId,
    otpType,
});

const otpResend = (userId: string, otpType: string) => ({
    type: actionTypes.OTP_RESEND,
    userId,
    otpType,
});

const otpVerify = (
    userId: string,
    otp: string,
    username: string,
    password: string,
    otpType: string
) => ({
    type: actionTypes.OTP_VERIFY,
    userId,
    otp,
    username,
    password,
    otpType,
});




const requestEmailSignup = (email: string) => ({
    type: actionTypes.REQUEST_EMAIL_SIGNUP,
    email,
});

const requestPasswordSignup = (userId: string, password: string) => ({
    type: actionTypes.REQUEST_PASSWORD_SIGNUP,
    userId,
    password,
});

const requestCheckDefaultPassword = (userId: string, defaultPassword: string) => ({
    type: actionTypes.REQUEST_DEFAULT_PASSWORD,
    userId,
    defaultPassword,
});

const requestSetPassword = (userId: string, defaultPassword: string, password: string) => ({
    type: actionTypes.REQUEST_SET_PASSWORD,
    userId,
    defaultPassword,
    password,
});

const canceledCheckDefaultPassword = () => ({
    type: actionTypes.CANCELED_DEFAULT_PASSWORD,
});
const requestGeneratePassword = (mobileNumber: number) => ({
    type: actionTypes.REQUEST_GENERATE_PASSWORD,
    mobileNumber,
});

const canceledGeneratePassword = () => ({
    type: actionTypes.CANCELED_GENERATE_PASSWORD,
});
const canceledSetPassword = () => ({ type: actionTypes.CANCELED_SET_PASSWORD });
const resetChangePasswordState = () => ({
    type: actionTypes.RESET_CHANGE_PASSWORD_STATE,
});
const resetSignup = () => ({
    type: actionTypes.RESET_SIGNUP,
});
const requestMobileSignup = (
    mobileNumber: number,
    existingAccount: boolean
) => ({
    type: actionTypes.REQUEST_MOBILE_SIGNUP,
    mobileNumber,
    existingAccount
});
const resetRegisterMobile = () => ({
    type: actionTypes.RESET_REGISTER_MOBILE,
});

const resetVerifyMobile = () => ({
    type: actionTypes.RESET_VERIFY_MOBILE,
});
const resetNumber = () => ({
    type: actionTypes.RESET_NUMBER,
});

const emailVerify = (userId: string, emailId: string, otpType: string) => ({
    type: actionTypes.EMAIL_VERIFY_OTP_SEND,
    userId,
    emailId,
    otpType,
});

const emailResend = (userId: string, emailId: string, otpType: string) => ({
    type: actionTypes.EMAIL_VERIFY_OTP_RESEND,
    userId,
    emailId,
    otpType,
});

const emailVerifyOtpVerify = (userId: string, userOtp: number, otpType: string, newEmailId: string) => ({
    type: actionTypes.EMAIL_VERIFY_OTP_VERIFY,
    userId,
    userOtp,
    otpType,
    newEmailId,
});

const emailOtpClear = () => ({
    type: actionTypes.EMAIL_VERIFY_OTP_CLEAR,
});

const emailOtpErrorDismiss = () => ({
    type: actionTypes.EMAIL_VERIFY_OTP_ERROR_DISMISS,
});
const getUserProfile = (userId: string) => ({
    type: actionTypes.REQUEST_USER_PROFILE,
    userId,
});









const requestVerifyMobile = (userId: string, otp: number) => ({
    type: actionTypes.REQUEST_MOBILE_VERIFY,
    userId,
    otp,
});

const requestResendOtp = (userId: string, phoneNumber: number, otpType: string) => ({
    type: actionTypes.REQUEST_RESEND_OTP,
    userId,
    phoneNumber,
    otpType,
});

const requestCancelVerifyOtp = (payload = false) => ({
    type: actionTypes.CANCEL_VERIFY_OTP,
    payload,
});



// Exported Actions and Events
export const actions = {
    common: {
        setCurrentRouteKey,
    },
    locale: {
        setLang,
    },
    theme: {
        setDirection,
        setMode,
        setLayout,
        setPreviousLayout,
        setSideNavCollapse,
        setNavMode,
        setPanelExpand,
        setThemeColor,
        setThemeColorLevel,
    },
    loading: {
        requestLoadingAction,
        completeLoadingAction,
    },
    error: {
        cancelErrorAction,
    },
    signup: {
        createAccount,
        requestOtpAccountActivation,
        registerNewCompany,




        requestEmailSignup,
        requestPasswordSignup,
        resetSignup,
        requestMobileSignup,
        requestVerifyMobile,
        requestResendOtp,
        requestCancelVerifyOtp,
        resetRegisterMobile,
        resetVerifyMobile,
        resetNumber,
    },
    auth: {
        requestLogin,
        request2faLogin,
        updateAuthToken,
        verifyUseranme,



        requestChangePassword,
        resetPassword,
        destroySession,
        lockSession,
    },
    company: {
        fetchCompany,
    },
    profile: {
        fetchSubordinates,
    },



    account: {
        createAccount,
        initiateProfileUpdate,
    },


    otp: {
        otpSend,
        otpResend,
        otpVerify,
    },
    checkDefaultPassword: {
        requestCheckDefaultPassword,
        canceledCheckDefaultPassword,
    },
    generatePassword: {
        requestGeneratePassword,
        canceledGeneratePassword,
    },
    setPassword: {
        requestSetPassword,
        canceledSetPassword,
        requestChangePassword,
    },
    changePassword: {
        resetChangePasswordState,
    },

    emailVerify: {
        emailVerify,
        emailResend,
        emailVerifyOtpVerify,
        emailOtpClear,
        emailOtpErrorDismiss,
    },
    userProfile: {
        getUserProfile,
    },
    ncbsc: { ...ncbscActions },
    complaint: {...complaintActions},
    appeal: {...appealActions}
};

export const events = {
    common: {
        SET_CURRENT_ROUTE_KEY: actionTypes.SET_CURRENT_ROUTE_KEY,
    },
    locale: {
        SET_LOCALE: actionTypes.SET_LANG,
    },
    theme: {
        SET_DIRECTION: actionTypes.SET_DIRECTION,
        ACTIVATE_MODE: actionTypes.SET_MODE,
        ACTIVATE_LAYOUT: actionTypes.SET_LAYOUT,
        SET_PREV_LAYOUT: actionTypes.SET_PREVIOUS_LAYOUT,
        ACTIVATE_SIDE_NAV: actionTypes.SET_SIDE_NAV_COLLAPSE,
        ACTIVATE_NAV_MODE: actionTypes.SET_NAV_MODE,
        ACTIVATE_PANEL: actionTypes.SET_PANEL_EXPAND,
        SET_THEME_COLOR: actionTypes.SET_THEME_COLOR,
        SET_THEME_COLOR_LEVEL: actionTypes.SET_THEME_COLOR_LEVEL,
    },
    loading: {
        RECEIVED: actionTypes.LOADING_RECEIVED,
        COMPLETED: actionTypes.LOADING_COMPLETED,
    },
    error: {
        RECEIVED: actionTypes.ERROR_RECEIVED,
        CANCELED: actionTypes.ERROR_CANCELED,
    },
    signup: {
        INITIATE_CREATE_ACCOUNT: actionTypes.REQUEST_CREATE_ACCOUNT,
        CREATE_ACCOUNT_SUCCESS: actionTypes.RECEIVED_CREATE_ACCOUNT,
        CREATE_ACCOUNT_FAIL: actionTypes.FAILED_CREATE_ACCOUNT,
        REQUEST_ACCOUNT_ACTIVATION: actionTypes.REQUEST_ACCOUNT_ACTIVATION,
        RECEIVED_ACCOUNT_ACTIVATION: actionTypes.RECEIVED_ACCOUNT_ACTIVATION,
        FAILED_ACCOUNT_ACTIVATION: actionTypes.FAILED_ACCOUNT_ACTIVATION,
        REGISTER_COMPANY_REQUEST: actionTypes.REGISTER_COMPANY_REQUEST,
        REGISTER_COMPANY_RECEIVED: actionTypes.REGISTER_COMPANY_SUCCESS,
        REGISTER_COMPANY_FAILED: actionTypes.REGISTER_COMPANY_FAILURE,







        REQUEST_OTP: actionTypes.REQUEST_MOBILE_VERIFY,
        RECEIVED_OTP: actionTypes.RECEIVED_MOBILE_VERIFY,
        FAILED_OTP: actionTypes.FAILED_MOBILE_VERIFY,






        REQUEST_EMAIL: actionTypes.REQUEST_EMAIL_SIGNUP,
        EMAIL_RECEIVED: actionTypes.RECEIVED_EMAIL_SIGNUP,
        EMAIL_FAILED: actionTypes.FAILED_EMAIL_SIGNUP,
        REQUEST_PASSWORD: actionTypes.REQUEST_PASSWORD_SIGNUP,
        PASSWORD_RECEIVED: actionTypes.RECEIVED_PASSWORD_SIGNUP,
        PASSWORD_FAILED: actionTypes.FAILED_PASSWORD_SIGNUP,
        RESET_SIGNUP: actionTypes.RESET_SIGNUP,
        REQUEST_MOBILE: actionTypes.REQUEST_MOBILE_SIGNUP,
        MOBILE_RECEIVED: actionTypes.RECEIVED_MOBILE_SIGNUP,
        MOBILE_FAILED: actionTypes.FAILED_MOBILE_SIGNUP,
        RESET_REGISTER_MOBILE: actionTypes.RESET_REGISTER_MOBILE,



        RESET_VERIFY_MOBILE: actionTypes.RESET_VERIFY_MOBILE,
        REQUEST_RESEND_OTP: actionTypes.REQUEST_RESEND_OTP,
        RECEIVED_RESEND_OTP: actionTypes.RECEIVED_RESEND_OTP,
        FAILED_RESEND_OTP: actionTypes.FAILED_RESEND_OTP,
        CANCEL_VERIFY_OTP: actionTypes.CANCEL_VERIFY_OTP,
        DESTROY_SESSION: actionTypes.DESTROY_SESSION,
        RESET_NUMBER: actionTypes.RESET_NUMBER,
    },
    login: {
        REQUEST: actionTypes.REQUEST_LOGIN,
        RECEIVED: actionTypes.RECEIVED_LOGIN,
        FAILED: actionTypes.FAILED_LOGIN,
        CANCELED: actionTypes.CANCELED_LOGIN,
    },
    multiFactor: {
        REQUEST: actionTypes.REQUEST_2FA_LOGIN,
        RECEIVED: actionTypes.RECEIVED_2FA_LOGIN,
        FAILED: actionTypes.FAILED_2FA_LOGIN,
        CANCELED: actionTypes.CANCELED_2FA_LOGIN,
    },
    auth: {
        REQUEST_REFRESH_JWT_TOKEN: actionTypes.REQUEST_REFRESH_JWT_TOKEN,
        RECEIVED_REFRESH_JWT_TOKEN: actionTypes.RECEIVED_REFRESH_JWT_TOKEN,
        FAILED_REFRESH_JWT_TOKEN: actionTypes.FAILED_REFRESH_JWT_TOKEN,
        LOCK_SESSION: actionTypes.LOCK_SESSION,
        REQUEST_USERNAME_VERIFY: actionTypes.REQUEST_USERNAME_VERIFY,
        RECEIVED_USERNAME_VERIFY: actionTypes.RECEIVED_USERNAME_VERIFY,
        FAILED_USERNAME_VERIFY: actionTypes.FAILED_USERNAME_VERIFY,
    },
    profile:{
        FETCH_AGENTS: actionTypes.FETCH_AGENTS,
        FETCH_SUBORDINATESSUCCESS: actionTypes.FETCH_SUBORDINATESSUCCESS,
        FETCH_SUBORDINATESFAILURE: actionTypes.FETCH_SUBORDINATESFAILURE,
    },
    company: {
        FETCH_COMPANY_REQUEST: actionTypes.FETCH_COMPANY_REQUEST,
        FETCH_COMPANY_SUCCESS: actionTypes.FETCH_COMPANY_SUCCESS,
        FETCH_COMPANY_FAILURE: actionTypes.FETCH_COMPANY_FAILURE,
    },





    account: {
        new: {
            INITIATE_CREATE_ACCOUNT: actionTypes.REQUEST_CREATE_ACCOUNT,
            CREATE_ACCOUNT_SUCCESS: actionTypes.RECEIVED_CREATE_ACCOUNT,
            CREATE_ACCOUNT_FAIL: actionTypes.FAILED_CREATE_ACCOUNT,
        },
        update: {
            INITIATE_ACCOUNT_UPDATE: actionTypes.ACCOUNT_UPDATE_REQUEST,
            ACCOUNT_UPDATE_SUCCESS: actionTypes.ACCOUNT_UPDATE_SUCCESS,
            ACCOUNT_UPDATE_FAIL: actionTypes.ACCOUNT_UPDATE_FAILED,
        }
    },





    checkDefaultPassword: {
        REQUEST: actionTypes.REQUEST_DEFAULT_PASSWORD,
        RECEIVED: actionTypes.RECEIVED_DEFAULT_PASSWORD,
        FAILED: actionTypes.FAILED_DEFAULT_PASSWORD,
        CANCELED: actionTypes.CANCELED_DEFAULT_PASSWORD,
    },
    generatePassword: {
        REQUEST: actionTypes.REQUEST_GENERATE_PASSWORD,
        RECEIVED: actionTypes.RECEIVED_GENERATE_PASSWORD,
        FAILED: actionTypes.FAILED_GENERATE_PASSWORD,
        CANCELED: actionTypes.CANCELED_GENERATE_PASSWORD,
    },
    setPassword: {
        REQUEST: actionTypes.REQUEST_SET_PASSWORD,
        RECEIVED: actionTypes.RECEIVED_SET_PASSWORD,
        FAILED: actionTypes.FAILED_SET_PASSWORD,
        CANCELED: actionTypes.CANCELED_SET_PASSWORD,
    },
    changePassword: {
        REQUEST: actionTypes.REQUEST_CHANGE_PASSWORD,
        RECIEVED: actionTypes.RECEIVED_CHANGE_PASSWORD,
        FAILED: actionTypes.FAILED_CHANGE_PASSWORD,
        RESET: actionTypes.RESET_CHANGE_PASSWORD_STATE,
    },
    otp: {
        OTP_SEND: actionTypes.OTP_SEND,
        OTP_RESEND: actionTypes.OTP_RESEND,
        OTP_RECEIVED: actionTypes.OTP_RECEIVED,
        OTP_FAILED: actionTypes.OTP_FAILED,
        OTP_VERIFY: actionTypes.OTP_VERIFY,
        OTP_VERIFY_SUCCESS: actionTypes.OTP_VERIFY_SUCCESS,
        OTP_VERIFY_FAILED: actionTypes.OTP_VERIFY_FAILED,
    },
    emailVerify: {
        EMAIL_VERIFY_OTP_SEND: actionTypes.EMAIL_VERIFY_OTP_SEND,
        EMAIL_VERIFY_OTP_RESEND: actionTypes.EMAIL_VERIFY_OTP_RESEND,
        EMAIL_VERIFY_OTP_RECEIVED: actionTypes.EMAIL_VERIFY_OTP_RECEIVED,
        EMAIL_VERIFY_OTP_FAILED: actionTypes.EMAIL_VERIFY_OTP_FAILED,
        EMAIL_VERIFY_OTP_CLEAR: actionTypes.EMAIL_VERIFY_OTP_CLEAR,
        EMAIL_VERIFY_OTP_ERROR_DISMISS: actionTypes.EMAIL_VERIFY_OTP_ERROR_DISMISS,
        EMAIL_VERIFY_OTP_VERIFY: actionTypes.EMAIL_VERIFY_OTP_VERIFY,
        EMAIL_VERIFY_OTP_VERIFY_SUCCESS: actionTypes.EMAIL_VERIFY_OTP_VERIFY_SUCCESS,
        EMAIL_VERIFY_OTP_VERIFY_FAILED: actionTypes.EMAIL_VERIFY_OTP_VERIFY_FAILED,
        EMAIL_VERIFIED_SUCCESS_UPDATE: actionTypes.EMAIL_VERIFIED_SUCCESS_UPDATE,
    },
    ncbsc: { ...ncbscEvents },
    complaint: {...complaintEvents},
    appeal: {...appealEvents}
};
