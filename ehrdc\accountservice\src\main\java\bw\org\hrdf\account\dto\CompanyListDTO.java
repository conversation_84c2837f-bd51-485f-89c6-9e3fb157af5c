package bw.org.hrdf.account.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class CompanyListDTO {
    private String uuid;
    private String name;
    private String type;
    private String contactPerson;
    private String referenceNumber;
    private String status;
    private String state;
    private LocalDateTime applicationDate;
    private String agentLead;
    private String agent;
    private String manager;
}
