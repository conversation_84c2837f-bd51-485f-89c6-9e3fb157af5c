import { Tooltip } from '@/components/ui';
import { IoMdEye } from 'react-icons/io';
import { MdChangeCircle, MdDelete, MdDownload, MdEdit, MdGavel, MdSpeakerNotes } from 'react-icons/md'
import systemIcon from '@/configs/icon.config'

interface ActionColumnProps {
    isViewingAllowed?: boolean
    isEditingAllowed?: boolean
    isDeletingAllowed?: boolean
    isAppealAllowed?: boolean
    isLoggingComplaintAllowed?: boolean
    isDownloadAllowed?: boolean
    isChangeRequestAllowed?:boolean
    isPaymentAllowed?:boolean
    onEdit?: () => void;
    onView?: () => void;
    onDelete?: () => void;
    onDownload?: () => void;
    onAppeal?: () => void;
    onLogComplaint?: () => void;
    onChangeRequest?: () => void;
    onPayment?:()=>void
}

const ActionColumn = (props: ActionColumnProps) => {
    const {
        isViewingAllowed,
        isEditingAllowed,
        isDeletingAllowed,
        isAppealAllowed,
        isLoggingComplaintAllowed,
        isDownloadAllowed,
        isChangeRequestAllowed,
        isPaymentAllowed,
        onEdit,
        onDownload,
        onView,
        onDelete,
        onAppeal,
        onLogComplaint,
        onChangeRequest,
        onPayment
    } = props

    return (
        <div className="flex gap-2 relative" >
            {isViewingAllowed && onView && (
                <Tooltip title={"View"}>
                    <div
                        className="bg-gray-100 hover:bg-gray-300 flex text-gray-500 gap-2 p-2 cursor-pointer select-none items-center rounded-lg font-semibold"
                        onClick={onView}>
                        {systemIcon.VIEW}
                    </div>
                </Tooltip>
            )}
            {isEditingAllowed && onEdit && (
                <Tooltip title={"Edit"}>
                    <div className="bg-gray-100 hover:bg-gray-300 rounded-md cursor-pointer select-none font-semibold p-2"
                         onClick={onEdit}>
                        {systemIcon.EDIT}
                    </div>
                </Tooltip>
            )}
            {isDeletingAllowed && onDelete && (
                <Tooltip title={"Delete"}>
                    <div
                        className="bg-gray-100 text-red-500  p-2 cursor-pointer select-none  hover:bg-red-100 rounded-lg font-semibold"
                        onClick={onDelete}>
                        {systemIcon.DELETE}
                    </div>
                </Tooltip>
            )}
            {isDownloadAllowed && onDownload && (
                <Tooltip title={"Download"}>
                    <div className="bg-gray-100 hover:bg-gray-300 rounded-md cursor-pointer select-none font-semibold p-2"
                         onClick={onDownload}>
                        {systemIcon.DOWNLOAD}
                    </div>
                </Tooltip>
            )}
            {isAppealAllowed && onAppeal && (
                <Tooltip title={"Appeal"}>
                    <div className="bg-gray-100 hover:bg-gray-300 rounded-md cursor-pointer select-none font-semibold p-2"
                         onClick={onAppeal}>
                        {systemIcon.APPEAL}
                    </div>
                </Tooltip>
            )}
            {isLoggingComplaintAllowed && onLogComplaint && (
                <Tooltip title={"Log Complaint"}>
                    <div className="bg-gray-100 hover:bg-gray-300 rounded-md cursor-pointer select-none font-semibold p-2"
                         onClick={onLogComplaint}>
                        {systemIcon.COMPLAINT}
                    </div>
                </Tooltip>
            )}
            {isChangeRequestAllowed && onChangeRequest && (
                <Tooltip title={"Notification of Changes"}>
                    <div className="bg-gray-100 hover:bg-gray-300 rounded-md cursor-pointer select-none font-semibold p-2"
                         onClick={onChangeRequest}>
                        {systemIcon.NOTIFICATION_OF_CHANGE}
                    </div>
                </Tooltip>
            )}
            {isPaymentAllowed && onPayment && (
                <Tooltip title={"View Invoice"}>
                    <div className="bg-gray-100 hover:bg-gray-300 rounded-md cursor-pointer select-none font-semibold p-2"
                         onClick={onPayment}>
                        {systemIcon.PAYMENT}
                    </div>
                </Tooltip>
            )}
        </div>
    );
};

export default ActionColumn;
