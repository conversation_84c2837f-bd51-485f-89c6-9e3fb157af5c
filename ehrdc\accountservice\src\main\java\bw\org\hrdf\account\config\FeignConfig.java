package bw.org.hrdf.account.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import feign.codec.Decoder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class FeignConfig {

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Bean
    public Decoder customDecoder() {
        return (response, type) -> {
            if (response.body() == null) {
                return null;
            }
            try (var reader = response.body().asReader()) {
                // Deserialize response body into the desired type
                return objectMapper.readValue(reader, objectMapper.constructType(type));
            }
        };
    }
}
