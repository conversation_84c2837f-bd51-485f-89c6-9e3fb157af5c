package bw.org.hrdf.api;

import bw.org.hrdf.helper.ApiResponse;
import bw.org.hrdf.models.otp.OtpRequest;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestBody;

@Component
public class OtpClientFallback implements OtpClient {

    @Override
    public ApiResponse<?> generateOtp(@RequestBody OtpRequest otpRequest){
        return new ApiResponse<>(
                false,
                "OTP Service is unavailable",
                null,
                null
        );
    }

    @Override
    public ApiResponse<?> verifyOtp(@RequestBody OtpRequest otpRequest){
        return new ApiResponse<>(
                false,
                "OTP Service is unavailable",
                null,
                null
        );
    }
}
