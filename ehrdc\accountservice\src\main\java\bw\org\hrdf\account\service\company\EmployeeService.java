package bw.org.hrdf.account.service.company;

import bw.org.hrdf.account.entity.company.Employee;
import bw.org.hrdf.account.helper.BaseSpecifications;
import bw.org.hrdf.account.repositories.company.EmployeeRepository;
import jakarta.transaction.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
public class EmployeeService {

    @Autowired
    private EmployeeRepository employeeRepository;

    public Optional<Employee> findById(String id) {
        return employeeRepository.findByUuid(id);
    }

    public Page<Map<String, Object>> findAllCompanyEmployees(String companyId, PageRequest pageable) {

        Specification<Employee> spec = Specification.where(BaseSpecifications.isNotDeleted());
        spec = spec.and((root, query, criteriaBuilder) -> criteriaBuilder.equal(root.get("company").get("uuid"), companyId));

        Page<Employee> entities = employeeRepository.findAll(spec, pageable);
        if(entities.getSize() > 1){
            return entities.map(entity ->{
                Map<String, Object> props = new HashMap<>();
                props.put("uuid", entity.getUuid());
                props.put("firstName", entity.getFirstName());
                props.put("lastName", entity.getLastName());
                props.put("idNumber", entity.getIdNumber());
                props.put("idType", entity.getIdType().name());
                props.put("gender", entity.getGender());
                props.put("contactNumber", entity.getContactNumber());
                props.put("basicSalary", entity.getBasicSalary());
                props.put("idVerificationStatus", entity.getIdVerificationStatus().name());
                return props;
            });
        }
        return null;
    }

    public Employee save(Employee employee) {
        return employeeRepository.save(employee);
    }

    public Optional<Employee> update(String id, Employee updatedEmployee) {
        Optional<Employee> existingEmployee = findById(id);
        if (existingEmployee.isPresent()) {
            Employee employee = existingEmployee.get();
            employee.setFirstName(updatedEmployee.getFirstName());
            employee.setLastName(updatedEmployee.getLastName());
            employee.setIdNumber(updatedEmployee.getIdNumber());
            employee.setIdType(updatedEmployee.getIdType());
            employee.setGender(updatedEmployee.getGender());
            employee.setContactNumber(updatedEmployee.getContactNumber());
            employee.setBasicSalary(updatedEmployee.getBasicSalary());
            employee.setIdVerificationStatus(updatedEmployee.getIdVerificationStatus());
            return Optional.of(employeeRepository.save(employee));
        } else {
            return Optional.empty();
        }
    }

    public Optional<Employee> deleteById(String id) {
        Optional<Employee> existingEmployee = findById(id);
        if (existingEmployee.isPresent()) {
            Employee employee = existingEmployee.get();
            employee.setDeleted(true);

            return Optional.of(employeeRepository.save(employee));
        } else {
            return Optional.empty();
        }
    }

    @Transactional()
    public List<Employee> saveList(Set<Employee> employees) {
        return employeeRepository.saveAllAndFlush(employees);
    }
}
