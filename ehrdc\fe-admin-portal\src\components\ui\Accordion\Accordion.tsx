import React, { useState } from 'react';
import { IoIosArrowDroprightCircle } from "react-icons/io";
import { IoIosArrowDropright } from "react-icons/io";
import { IoIosArrowDropdownCircle } from "react-icons/io";

export interface AccordionProps {
  title: string;
  children: React.ReactNode;
}

const Accordion: React.FC<AccordionProps> = ({ title, children }) => {
  const [isOpen, setIsOpen] = useState(false);

  const toggleAccordion = () => {
    setIsOpen(!isOpen);
  };

  return (
    <div className="w-full mt-4 pt-4 ps-5 pb-4 h-auto bg-white flex flex-col justify-start items-start rounded-2xl shadow cursor-pointer" onClick={toggleAccordion}>
      {/* Title and Download Button */}
      <div className="w-full py-2 flex justify-between items-center" onClick={toggleAccordion}>
        {/* Title */}
        <div className="flex-grow flex items-center gap-2.5">
          <div className="text-[#1b2128] text-xl font-semibold font-['Poppins']">
            {title}
          </div>
        </div>
        <div
          className="w-auto h-[41px] mx-3 px-3 py-3  rounded-full shadow flex justify-center items-center"
        >
          <div className="text-dark text-4xl font-bold font-['Roboto']">
          {isOpen && <IoIosArrowDropdownCircle /> }
          {!isOpen && <IoIosArrowDropright /> }
          
          </div>
        </div>
      </div>
      {isOpen && (
        <div className="w-full rounded-2xl mt-2 p-4">
          {children}
        </div>
      )}
    </div>
    // <div className="w-full h-auto relative">
    //   <div className="w-full h-24 bg-white rounded-2xl shadow cursor-pointer" onClick={toggleAccordion}>
    //     <div className="w-96 h-12 left-0 top-0 absolute">
    //       <div className="w-96 left-0 top-11 absolute text-indigo-950 text-lg font-bold font-['Poppins'] leading-7 ps-5">
    //         {title}
    //       </div>
    //       <div className="w-12 h-12 float-right top-0 absolute">
    //         <div className="w-12 h-12 relative bg-white rounded-full shadow">
    //             <IoIosArrowDropdown />
    //         </div>
    //       </div>
    //     </div>
    //   </div>
    //   {isOpen && (
    //     <div className="w-96 bg-white rounded-2xl shadow mt-2 p-4">
    //       {children}
    //     </div>
    //   )}
    // </div>
  );
};

export default Accordion;
export type { AccordionProps };
