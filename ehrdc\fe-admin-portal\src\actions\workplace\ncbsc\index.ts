import { ActionTypes as actionTypes } from './actionTypes'

// Generic Action Interface
interface Action<T = any> {
    type: string;
    payload?: T;
}

//Fetch recognition applications
const getRecognitionApplications = (status:string,pageNumber:number,size:number): Action => ({
    type: actionTypes.FETCH_NCBSC_REQUESTS,
    payload:{applicationStatus:status,pageNumber,size}
})

const getCurrentStep = (): Action => ({
    type: actionTypes.FETCH_CURRENT_STEP
})
const getStepStatus = (): Action => ({
    type: actionTypes.FETCH_STEP_STATUS
})
const setCurrentStep = (currentStep: number): Action => ({
    type: actionTypes.SET_CURRENT_STEP,
    payload: { currentStep }
})
const setStepStatus = (stepStatus: string): Action => ({
    type: actionTypes.SET_STEP_STATUS,
    payload: { stepStatus }
})
const nextStep = (): Action => ({
    type: actionTypes.SET_NEXT_STEP
})

const previousStep = (): Action => ({
    type: actionTypes.SET_PREVIOUS_STEP
})

const jumpToStep = (step: number): Action => ({
    type: actionTypes.JUMP_TO_STEP,
    payload: { step }
})
const setFormData = (data:any): Action => ({
    type: actionTypes.SET_FORM_DATA,
    payload: { data }
})
// Exported Actions and Events
export const actions = {
    recognition: {
        form: {
            step: {
                getStepStatus,
                getCurrentStep,
                setCurrentStep,
                setStepStatus,
                jumpToStep,
                previousStep,
                nextStep,
                setFormData
            }
        },
        getRecognitionApplications
    }
}

export const events = {
    recognition: {
        fetch: {
            REQUEST: actionTypes.FETCH_NCBSC_REQUESTS,
            RECEIVED: actionTypes.FETCH_NCBSC_REQUESTS_SUCCESS,
            FAILED: actionTypes.FETCH_NCBSC_REQUESTS_FAILED
        },
        form: {
            step: {
                FETCH_STEP_STATUS:actionTypes.FETCH_STEP_STATUS,
                FETCH_CURRENT_STEP:actionTypes.FETCH_CURRENT_STEP,
                SET_NEXT_STEP:actionTypes.SET_NEXT_STEP,
                JUMP_TO_STEP:actionTypes.JUMP_TO_STEP,
                SET_PREVIOUS_STEP:actionTypes.SET_PREVIOUS_STEP,
                SET_STEP_STATUS:actionTypes.SET_STEP_STATUS,
                SET_CURRENT_STEP:actionTypes.SET_CURRENT_STEP,
                SET_FORM_DATA:actionTypes.SET_FORM_DATA
            }
        }

    }
}
