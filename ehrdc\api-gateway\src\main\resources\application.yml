server:
  port: 8072

spring:
  application:
    name: api-gateway
  cloud:
    gateway:
      discovery:
        locator:
          enabled: false
          lowerCaseServiceId: true
      httpclient:
        connect-timeout: 5000
        response-timeout: 10s
      globalcors:
        cors-configurations:
          '[/**]':
            allowedOrigins: "*"
            allowedMethods: "*"
            allowedHeaders: "*"
  data:
    redis:
      connect-timeout: 2s
      host: localhost
      port: 6379
      timeout: 1s
  security:
    oauth2:
      resourceserver:
        jwt:
          issuer-uri: https://hrdcdev.weblogic.co.bw/keycloak/realms/hrdc
          jwk-set-uri: https://hrdcdev.weblogic.co.bw/keycloak/realms/hrdc/protocol/openid-connect/certs

management:
  endpoints:
    web:
      exposure:
        include: "*"
  endpoint:
    gateway:
      enabled: true
  info:
    env:
      enabled: true

info:
  app:
    name: api-gateway
    description: eHRDF Gateway Server Application
    version: "1.0.0"

logging:
  level:
    bw:
      org:
        hrdc:
          weblogic:
            apigateway: DEBUG

resilience4j.circuitbreaker:
  configs:
    default:
      slidingWindowSize: 10
      permittedNumberOfCallsInHalfOpenState: 2
      failureRateThreshold: 50
      waitDurationInOpenState: 10000

eureka:
  instance:
    preferIpAddress: true
  client:
    fetchRegistry: true
    registerWithEureka: true
    serviceUrl:
      defaultZone: http://localhost:8070/eureka/
