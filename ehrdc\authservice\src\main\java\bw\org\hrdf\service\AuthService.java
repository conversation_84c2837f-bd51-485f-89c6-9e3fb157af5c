package bw.org.hrdf.service;

import bw.org.hrdf.entity.enums.CommunicationType;
import bw.org.hrdf.entity.enums.OTPTypes;
import bw.org.hrdf.models.login.AutologinRequest;
import bw.org.hrdf.models.otp.OtpRequest;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import org.keycloak.OAuth2Constants;
import org.keycloak.admin.client.Keycloak;
import org.keycloak.admin.client.resource.UserResource;
import org.keycloak.representations.idm.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class AuthService {

    @Autowired
    private Keycloak keycloak;

    @Value("${keycloak.realm}")
    private String realm;

    @Value("${keycloak.server-url}")
    private String keycloakServerUrl;

    @Value("${keycloak.client-id}")
    private String clientId;

    @Value("${keycloak.client-secret}")
    private String clientSecret;

    @Autowired
    private UserSettingsService settingsService;

    public UserRepresentation getUserByUsername(String username) {
        return keycloak.realm(realm)
                .users()
                .search(username, 0, 1)
                .stream()
                .findFirst()
                .orElse(null);
    }
    private MultiValueMap<String, String> setUpRequestHeaders(String email, String password){
        MultiValueMap<String, String> map = new LinkedMultiValueMap<>();
        map.add("grant_type", "password");
        map.add("client_id", clientId);
        map.add("client_secret", clientSecret);
        map.add("username", email);
        map.add("password", password);
        return map;
    }
    public ResponseEntity<String> authenticateUser(String username, String password) {

        String url = String.format("%s/realms/%s/protocol/openid-connect/token", keycloakServerUrl, realm);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));

        MultiValueMap<String, String>  requestBody = setUpRequestHeaders(username, password);

        HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(requestBody, headers);
        RestTemplate restTemplate = new RestTemplate();

        return restTemplate.postForEntity(url, request, String.class);
    }


    public UserRepresentation fetchUserByUserId(String userId){
        return keycloak.realm(realm).users().get(userId).toRepresentation();
    }

    public ResponseEntity<String> generateRefreshToken(String refreshToken) {
        String url = String.format("%s/realms/%s/protocol/openid-connect/token", keycloakServerUrl, realm);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));

        MultiValueMap<String, String> requestBody = new LinkedMultiValueMap<>();
        requestBody.add("grant_type", OAuth2Constants.REFRESH_TOKEN);
        requestBody.add("client_id", clientId);
        requestBody.add("client_secret", clientSecret);
        requestBody.add("refresh_token", refreshToken);

        HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(requestBody, headers);
        RestTemplate restTemplate = new RestTemplate();

        return restTemplate.postForEntity(url, request, String.class);
    }

    public void assignRoleToUser(String userId, List<String> clientRoles) {
        if (clientRoles != null) {
            for (String clientRole : clientRoles) {
                ClientRepresentation clientResource = keycloak.realm(realm).clients().findByClientId(clientId).get(0);

                RoleRepresentation userClientRole = keycloak.realm(realm).clients().get(clientResource.getId())
                        .roles().get(clientRole).toRepresentation();

                keycloak.realm(realm).users().get(userId).roles().clientLevel(clientResource.getId()).add(List.of(userClientRole));
            }
        }
    }
    public Map<String, List<String>> getUserRoles(String userId) {

        try {
            Map<String, List<String>> userRoles = new HashMap<>();

            UserResource userResource = keycloak.realm(realm).users().get(userId);
            MappingsRepresentation userActiveRoles = userResource.roles().getAll();

            List<String> userActiveGroups = userResource.groups().stream()
                    .map(group -> group.getName().replace("-", "_").toUpperCase())
                    .toList();

            List<String> realmRoleNames = Optional.ofNullable(userActiveRoles.getRealmMappings())
                    .orElse(Collections.emptyList())
                    .stream()
                    .map(role -> role.getName().replace("-", "_").toUpperCase())
                    .toList();

            List<ClientRepresentation> clients = keycloak.realm(realm).clients().findByClientId(clientId);
            Set<String> allClientRoles = new HashSet<>();

            if (!clients.isEmpty()) {
                for (ClientRepresentation client : clients) {
                    List<RoleRepresentation> clientRoles = userResource.roles().clientLevel(client.getId()).listAll();
                    clientRoles.forEach(role -> allClientRoles.add(role.getName().replace("-", "_").toUpperCase()));
                }
            }


            Map<String, ClientMappingsRepresentation> clientMappings = userActiveRoles.getClientMappings();
            userRoles.put("realmRoles", realmRoleNames);
            userRoles.put("activeGroups", userActiveGroups);
            userRoles.put("roles", new ArrayList<>(allClientRoles));

            if(!clients.isEmpty()){
                for(ClientRepresentation client : clients){
                    List<RoleRepresentation> clientRoles = userResource.roles().clientLevel(client.getId()).listAll();
                    List<String> clientRoleNames = clientRoles.stream()
                            .map(role -> role.getName().replace("-", "_").toUpperCase())
                            .collect(Collectors.toList());
                    userRoles.put("roles", clientRoleNames);
                }
            }
            return userRoles;

        } catch (Exception e) {
            throw new RuntimeException("Failed to retrieve roles for user: " + userId, e);
        }
    }

    public UserRepresentation resetPassword(String userId, String newPassword) {
        CredentialRepresentation credentials = new CredentialRepresentation();
        credentials.setTemporary(false);
        credentials.setValue(newPassword);
        UserRepresentation userDetails = keycloak.realm(realm).users().get(userId).toRepresentation();
        keycloak.realm(realm).users().get(userId).resetPassword(credentials);
        return userDetails;
    }
    public void keycloakSessionLogout(String userId){
        try {
            keycloak.realm(realm).users().get(userId).logout();
        } catch (Exception e) {
            throw new RuntimeException("Error while logging out from the system", e);
        }
    }
    public void tomcatSessionLogout(HttpServletRequest request) throws ServletException {
        request.logout();
    }

    public void keycloakTerminateAllActiveSessionsLogout(String userId){
        try {
            keycloak.realm(realm).users().get(userId).getUserSessions().clear();
        } catch (Exception e) {
            throw new RuntimeException("Error while logging out from the system", e);
        }
    }

    public OtpRequest getOtpRequest(AutologinRequest loginRequest, UserRepresentation userDetails, OTPTypes otpType) {
        OtpRequest otpRequest = new OtpRequest();
        otpRequest.setUserId(userDetails.getId());
        otpRequest.setOtpType(otpType);
        if(loginRequest.isMobile()){
            otpRequest.setCommunicationType(CommunicationType.SMS);
            otpRequest.setContactAddress(userDetails.getUsername());
        }else{
            otpRequest.setCommunicationType(CommunicationType.EMAIL);
            otpRequest.setContactAddress(userDetails.getEmail());
        }

        otpRequest.setContactAddress(userDetails.getEmail());
        return otpRequest;
    }
}

