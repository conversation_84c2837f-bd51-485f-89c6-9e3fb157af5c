package bw.org.hrdf.account.helper;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ApiResponse<T> {
    private boolean status;
    private String message;
    private T data;
    private List<ErrorResponse> errors;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ErrorResponse {
        private String errorCode;
        private String errorMsg;
    }

    public static ResponseEntity<?> createErrorResponse(String errorCode, String message) {
        return ResponseEntity.status(HttpStatus.OK).body(
                new ApiResponse<>(false, message, null,
                        List.of(new ApiResponse.ErrorResponse(errorCode, message))
                )
        );
    }

    public static ResponseEntity<ApiResponse<?>> getInternalServerError(String message){
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(new ApiResponse<>(false, "Internal error occurred", null,
                        List.of(new ApiResponse.ErrorResponse("INTERNAL_SERVER_ERROR", message))));
    }
}

