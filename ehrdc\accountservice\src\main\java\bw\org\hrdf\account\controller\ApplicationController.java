package bw.org.hrdf.account.controller;

import bw.org.hrdf.account.dto.ActionDTO;
import bw.org.hrdf.account.dto.CompanyDTO;
import bw.org.hrdf.account.dto.CompanyListDTO;
import bw.org.hrdf.account.entity.ApplicationComments;
import bw.org.hrdf.account.entity.company.Actions;
import bw.org.hrdf.account.entity.company.ApplicationEntity;
import bw.org.hrdf.account.entity.company.CompanyEntity;
import bw.org.hrdf.account.entity.enums.ActionsEnum;
import bw.org.hrdf.account.entity.enums.StateEnum;
import bw.org.hrdf.account.entity.enums.StatusEnum;
import bw.org.hrdf.account.entity.enums.VerificationStatusEnum;
import bw.org.hrdf.account.helper.ApiResponse;
import bw.org.hrdf.account.models.company.ApplicationRequest;
import bw.org.hrdf.account.models.company.CompanyRequest;
import bw.org.hrdf.account.models.company.ApplicationStatusUpdatePayload;
import bw.org.hrdf.account.models.company.CompanySearchCriteria;
import bw.org.hrdf.account.service.ApplicationCommentsService;
import bw.org.hrdf.account.service.company.ApplicationService;
import bw.org.hrdf.account.service.company.CompanyService;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

import static bw.org.hrdf.account.helper.ApiResponse.getInternalServerError;
import static bw.org.hrdf.account.utils.ReferenceNumberGenerator.generateReferenceNumber;

/**
 * eHRDF Application Service Controller
 * This controller exposes URI to create, retrieve
 * update & delete account object in eHRDF.
 *
 * <AUTHOR> Ntlhe
 *
 */

@RestController
@RequestMapping("/api/v1/company/applications")
public class ApplicationController {

    private static final Logger logger = LoggerFactory.getLogger(ApplicationController.class);

    @Autowired
    private ApplicationService applicationService;

    @Autowired
    private CompanyService companyService;

    @Autowired
    private ApplicationCommentsService commentsService;

    @PostMapping("/new")
    public ResponseEntity<?> createNewApplication(@Valid @RequestBody ApplicationRequest applicationRequest) {
        logger.info("New application initiated for company : {}", applicationRequest.getCompanyId());
        try {
            Optional<CompanyEntity> fetchedCompanyDetails = companyService.getCompanyById(applicationRequest.getCompanyId());
            if (fetchedCompanyDetails.isPresent()) {
                CompanyEntity companyDetails = fetchedCompanyDetails.get();
                StatusEnum applicationStatus = StatusEnum.PENDING;
                StateEnum applicationState = StateEnum.DRAFT;
                if(companyDetails.getRegistrationApplication().getReferenceNumber().isEmpty()){
                    if (companyDetails.getVerifications().stream().allMatch(v -> VerificationStatusEnum.VERIFIED.equals(v.getStatus()))) {
                        applicationState = StateEnum.SUBMITTED;
                    }
                    String referenceNumber = generateReferenceNumber("app");
                    ApplicationEntity application = new ApplicationEntity(
                            referenceNumber,
                            applicationStatus,
                            applicationState,
                            companyDetails,
                            new ArrayList<>(),
                            LocalDateTime.now(),
                            applicationRequest.getAgentLead(),
                            null,
                            null
                    );

                    ApplicationEntity savedApplication = applicationService.saveApplication(application);

                    if (savedApplication == null) {
                        logger.error("Failed to generate new application for company id {}", companyDetails.getUuid());
                        return ApiResponse.createErrorResponse("APPLICATION_ERROR", "Failed to process application.");
                    }else{
                        //TODO Send notification to agent lead informing them of the new application
                        return ResponseEntity.ok(new ApiResponse<>(true, "Application created successfully", savedApplication, null));
                    }
                }else{
                    return ApiResponse.createErrorResponse("APPLICATION_EXIST", "Company has existing application");
                }
            }else{
                return ApiResponse.createErrorResponse("COMPANY_NOT_FOUND", "Provided company identifier does not exist");
            }
        } catch (Exception e) {
            logger.error("Create application for company {} failed with exception: {}", applicationRequest.getCompanyId(), e.getMessage());
            return getInternalServerError(e.getMessage());
        }
    }

    @PostMapping("/{applicationId}")
    public ResponseEntity<?> updateApplication(@PathVariable String applicationId, @RequestBody CompanyRequest applicationRequest) {
        logger.info("Application update initiated for application id : {}", applicationId);
        try {
            Optional<ApplicationEntity> fetchedApplication = applicationService.getApplicationByReferenceNumber(applicationRequest.getRegistrationApplication().getReferenceNumber());
            if (fetchedApplication.isPresent()) {
                ApplicationEntity existingApplication = fetchedApplication.get();
                existingApplication.setStatus(applicationRequest.getRegistrationApplication().getStatus());
                existingApplication.setState(applicationRequest.getRegistrationApplication().getState());
                ApplicationEntity savedApplication = applicationService.saveApplication(existingApplication);
                if (savedApplication == null) {
                    logger.error("Failed to update application id {}", applicationId);
                    return ApiResponse.createErrorResponse("APPLICATION_ERROR", "Failed to process application.");
                }else{
                    return ResponseEntity.ok(new ApiResponse<>(true, "Application created successfully", savedApplication, null));
                }
            }else{
                return ApiResponse.createErrorResponse("APPLICATION_NOT_FOUND", "Provided application reference does not exist");
            }
        } catch (Exception e) {
            logger.error("Failed to update application id {} with exception: {}", applicationId, e.getMessage());
            return getInternalServerError(e.getMessage());
        }
    }
    @PostMapping("/actions/{applicationId}")
    public ResponseEntity<?> addAction(@PathVariable String applicationId, @RequestBody ActionDTO actionRequest) {
        logger.info("Add action initiated for application id : {}", applicationId);
        try {
            Optional<ApplicationEntity> fetchedApplication = applicationService.getApplicationByUuid(applicationId);
            if (fetchedApplication.isPresent()) {
                Actions action = new Actions(
                        ActionsEnum.valueOf(actionRequest.getActionType().toUpperCase()),
                        actionRequest.getActionMessage(),
                        StatusEnum.PENDING,
                        fetchedApplication.get()
                );
                Actions savedAction = applicationService.createAction(action);
                if (savedAction == null) {
                    logger.error("Failed to create action for application id {}", applicationId);
                    return ApiResponse.createErrorResponse("APPLICATION_ACTION_ERROR", "Failed to create application action.");
                }else{
                    return ResponseEntity.ok(new ApiResponse<>(true, "Application action created successfully", savedAction, null));
                }
            }else{
                return ApiResponse.createErrorResponse("APPLICATION_NOT_FOUND", "Provided application reference does not exist");
            }
        } catch (Exception e) {
            logger.error("Add action  for application id {} failed with exception: {}", applicationId, e.getMessage());
            return getInternalServerError(e.getMessage());
        }
    }
    @PutMapping("/actions/{actionId}")
    public ResponseEntity<?> updateAction(@PathVariable String actionId, @RequestBody ActionDTO actionRequest) {
        logger.info("Update action initiated for action id : {}", actionId);
        try {
            Optional<Actions> fetchedAction = applicationService.getAction(actionId);
            if (fetchedAction.isPresent()) {
                Actions action = fetchedAction.get();
                action.setStatus(StatusEnum.valueOf(actionRequest.getActionStatus().toUpperCase()));
                Actions savedAction = applicationService.createAction(action);
                if (savedAction == null) {
                    logger.error("Failed to update action of id {}", actionId);
                    return ApiResponse.createErrorResponse("APPLICATION_ACTION_ERROR", "Failed to update application action.");
                }else{
                    return ResponseEntity.ok(new ApiResponse<>(true, "Application action updated successfully", savedAction, null));
                }
            }else{
                return ApiResponse.createErrorResponse("APPLICATION_ACTION_NOT_FOUND", "Provided application action reference does not exist");
            }
        } catch (Exception e) {
            logger.error("date action  for id {} failed with exception: {}", actionId, e.getMessage());
            return getInternalServerError(e.getMessage());
        }
    }

    @PostMapping("/{applicationReference}/assign-agent/{agentId}")
    public ResponseEntity<?> assignAgent(@PathVariable String applicationReference, @PathVariable String agentId) {
        logger.info("Application agent assignment initiated for application reference : {}", applicationReference);
        try {
            Optional<ApplicationEntity> fetchedApplication = applicationService.getApplicationByReferenceNumber(applicationReference);
            if (fetchedApplication.isPresent()) {
                ApplicationEntity existingApplication = fetchedApplication.get();
                existingApplication.setAgent(agentId);
                ApplicationEntity savedApplication = applicationService.saveApplication(existingApplication);
                if (savedApplication == null) {
                    logger.error("Failed to update application reference of {}", applicationReference);
                    return ApiResponse.createErrorResponse("APPLICATION_ERROR", "Failed to process application.");
                }else{
                    if(existingApplication.getAgent() != null && !existingApplication.getAgent().equals(agentId)){
                        //TODO application was reassigned, trigger notification to previous agent about the reassignment
                    }
                    //TODO trigger notification to agent for the assignment made

                    return ResponseEntity.ok(new ApiResponse<>(true, "Agent assigned successfully", null, null));
                }
            }else{
                return ApiResponse.createErrorResponse("APPLICATION_NOT_FOUND", "Provided application reference does not exist");
            }
        } catch (Exception e) {
            logger.error("Failed to assign application reference {} to agent id {} with exception: {}", applicationReference, agentId, e.getMessage());
            return getInternalServerError(e.getMessage());
        }
    }

    @PostMapping("/{offset}/{pageSize}")
    public ResponseEntity<ApiResponse<?>> getAllOwnerApplications(@PathVariable Integer offset, @PathVariable Integer pageSize,
                                                              @RequestBody CompanySearchCriteria searchCriteria) {
        try {
            if (offset == null)
                offset = 0;
            if(pageSize <= 0) {
                pageSize = 10;
            }

            Sort.Direction sortDirection = Sort.Direction.fromString(searchCriteria.getDirection().toUpperCase());
            Sort sort = Sort.by(sortDirection, searchCriteria.getSortBy());

            Page<CompanyListDTO> companies = companyService.getAllCompanies(
                    searchCriteria, PageRequest.of(offset, pageSize, sort));

            if (companies.isEmpty()) {
                return ResponseEntity.ok(new ApiResponse<>(false, "No records found", null, null));
            }

            Map<String, Object> metadata = new HashMap<>();
            metadata.put("currentPage", companies.getNumber());
            metadata.put("totalPages", companies.getTotalPages());
            metadata.put("totalElements", companies.getTotalElements());
            metadata.put("pageSize", companies.getSize());

            Map<String, Object> response = new HashMap<>();
            response.put("metadata", metadata);
            response.put("results", companies.getContent());

            return ResponseEntity.ok(new ApiResponse<>(true, "Records found", response, null));
        } catch (Exception e) {
            return getInternalServerError(e.getMessage());
        }
    }
    @GetMapping("/{referenceNumber}")
    public ResponseEntity<ApiResponse<?>> getApplicationByReference(@PathVariable String referenceNumber) {
        try {
            Optional<ApplicationEntity> applicationRes = applicationService.getApplicationByReferenceNumber(referenceNumber);
            if(applicationRes.isPresent()){
                Optional<CompanyDTO> applicationData = companyService.getCompanyByUuid(applicationRes.get().getCompany().getUuid());
                return applicationData.<ResponseEntity<ApiResponse<?>>>map(application -> ResponseEntity.ok(new ApiResponse<>(true, "Record found", application, null))).orElseGet(() -> ResponseEntity.status(HttpStatus.OK)
                        .body(new ApiResponse<>(false, "No record found", null, null)));
            }
            return ResponseEntity.ok(new ApiResponse<>(false, "No record found", null, null));
        } catch (Exception e) {
            return getInternalServerError(e.getMessage());
        }
    }

    @PutMapping("/{applicationReference}/status-update")
    public ResponseEntity<?> changeApplicationStatus(@PathVariable String applicationReference, @RequestBody ApplicationStatusUpdatePayload payload) {
        logger.info("Application status update initiated for application reference : {}", applicationReference);
        try {

            String role = payload.getRole();
            String userId = payload.getUserId();
            String newAssignee = payload.getNewAssignee();

            if(role.isEmpty() || userId.isEmpty()){
                return ApiResponse.createErrorResponse("MANDATORY_FIELDS_MISSING", "Required fields are missing, please provide user id and their respective role");
            }

            Optional<ApplicationEntity> application = applicationService.getApplicationByReferenceNumber(applicationReference);

            if (application.isPresent()) {
                int updatedResult = applicationService.updateApplicationEntity(applicationReference, role, newAssignee);
                if (updatedResult == 0) {
                    logger.error("Failed to update status of application reference of {}", applicationReference);
                    return ApiResponse.createErrorResponse("APPLICATION_ERROR", "Failed to process application.");
                }
                return ResponseEntity.ok(new ApiResponse<>(true, "Application assigned successfully", null, null));
            }
            return ApiResponse.createErrorResponse("APPLICATION_NOT_FOUND", "Provided application identifier does not exist");
        } catch (Exception e) {
            logger.error("Failed to update status of application reference {} with exception: {}", applicationReference, e.getMessage());
            return getInternalServerError(e.getMessage());
        }
    }
    @PutMapping("/status/{applicationReference}/update")
    public ResponseEntity<?> updateApplicationStatus(@PathVariable String applicationReference, @RequestBody Map<String, Object> payload) {
        logger.info("Application state and status update initiated for reference : {}", applicationReference);
        try {

            StateEnum state = StateEnum.valueOf(payload.get("state").toString());
            StatusEnum status = StatusEnum.valueOf(payload.get("status").toString());
            String userId = payload.get("userId").toString();
            String userRole = payload.get("role").toString();
            String comments = payload.containsKey("comments") ? payload.get("comments").toString() : "";

            Optional<ApplicationEntity> application = applicationService.getApplicationByReferenceNumber(applicationReference);

            if (application.isPresent()) {
                int updatedResult = applicationService.updateApplicationStatus(applicationReference, state, status);
                if (updatedResult == 0) {
                    logger.error("Failed to update application state and status of reference {}", applicationReference);
                    return ApiResponse.createErrorResponse("APPLICATION_ERROR", "Failed to process application.");
                }

                ApplicationComments logEntry = new ApplicationComments();
                logEntry.setApplication(application.get());
                logEntry.setAction(String.valueOf(status));
                logEntry.setComments(applicationService.sanitizeHtml(comments));
                logEntry.setUpdatedBy(userId);
                logEntry.setUpdateRole(userRole);
                logEntry.setTimestamp(LocalDateTime.now());

                commentsService.createComments(logEntry);
                return ResponseEntity.ok(new ApiResponse<>(true, "Application status updated successfully", null, null));
            }
            return ApiResponse.createErrorResponse("APPLICATION_NOT_FOUND", "Provided application identifier does not exist");
        } catch (Exception e) {
            logger.error("Failed to update application state and status of reference {} with exception: {}", applicationReference, e.getMessage());
            return getInternalServerError(e.getMessage());
        }
    }
}