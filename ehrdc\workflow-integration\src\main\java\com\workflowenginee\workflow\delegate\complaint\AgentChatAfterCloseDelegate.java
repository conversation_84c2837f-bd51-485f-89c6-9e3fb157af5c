package com.workflowenginee.workflow.delegate.complaint;

import java.util.Map;

import org.flowable.engine.delegate.DelegateExecution;
import org.flowable.engine.delegate.JavaDelegate;
import org.springframework.stereotype.Component;

import com.workflowenginee.workflow.dto.NotifyToClientDto;
import com.workflowenginee.workflow.service.NotificationComplaintService;
import com.workflowenginee.workflow.util.Enums;

@Component("agentChatAfterCloseDelegate")
public class AgentChatAfterCloseDelegate implements JavaDelegate {

    private final NotificationComplaintService notificationComplaintService;

    public AgentChatAfterCloseDelegate(NotificationComplaintService notificationComplaintService) {
        this.notificationComplaintService = notificationComplaintService;
    }

    @Override
    public void execute(DelegateExecution execution) {
        String processInstanceId = execution.getProcessInstanceId();
        String complaintId = (String) execution.getVariable("complaintId");
        String role = (String) execution.getVariable("role");

        System.out.println("[Process: " + processInstanceId + "] Agent initiating chat after closing complaint: " + complaintId);

        try {
            Map<String, Object> complaintData = (Map<String, Object>) execution.getVariable("complaintData");
            
            if (complaintData != null) {
                // Log the chat initiation
                System.out.println("[Process: " + processInstanceId + "] Agent is following up with client after closure");
                
                // Set chat session variables
                execution.setVariable("chatSessionActive", true);
                execution.setVariable("chatInitiatedBy", "AGENT");
                execution.setVariable("chatReason", "POST_CLOSURE_FOLLOWUP");
                execution.setVariable("chatStartTime", System.currentTimeMillis());

                // Notify client about chat availability
                try {
                    NotifyToClientDto clientNotification = NotifyToClientDto.builder()
                        .applicationType(Enums.ApplicationType.COMPLAINTS.name())
                        .applicationId(complaintId)
                        .notificationType(Enums.NotificationType.CHAT_AVAILABLE.name())
                        .message("Our agent is available for follow-up discussion regarding your closed complaint")
                        .build();

                    notificationComplaintService.notifyToClient(clientNotification);
                    
                    System.out.println("[Process: " + processInstanceId + "] Client notified about chat availability");
                    
                } catch (Exception notificationEx) {
                    System.err.println("[Process: " + processInstanceId + "] Failed to send chat notification: " + notificationEx.getMessage());
                }

                System.out.println("[Process: " + processInstanceId + "] Agent chat session initiated successfully");

            } else {
                System.err.println("[Process: " + processInstanceId + "] No complaint data available for chat initiation");
                execution.setVariable("chatSessionActive", false);
            }

        } catch (Exception e) {
            System.err.println("[Process: " + processInstanceId + "] Error initiating agent chat: " + e.getMessage());
            e.printStackTrace();
            execution.setVariable("chatSessionActive", false);
        }
    }
}
