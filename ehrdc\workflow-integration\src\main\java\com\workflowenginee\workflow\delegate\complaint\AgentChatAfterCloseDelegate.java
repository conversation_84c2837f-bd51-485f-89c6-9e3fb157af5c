package com.workflowenginee.workflow.delegate.complaint;

import java.util.Map;

import org.flowable.engine.delegate.DelegateExecution;
import org.flowable.engine.delegate.JavaDelegate;
import org.springframework.stereotype.Component;

@Component("agentChatAfterCloseDelegate")
public class AgentChatAfterCloseDelegate implements JavaDelegate {

    @Override
    public void execute(DelegateExecution execution) {
        String processInstanceId = execution.getProcessInstanceId();
        String complaintId = (String) execution.getVariable("complaintId");
        String role = (String) execution.getVariable("role");

        System.out.println("[Process: " + processInstanceId + "] Agent initiating chat after closing complaint: " + complaintId);

        try {
            Map<String, Object> complaintData = (Map<String, Object>) execution.getVariable("complaintData");
            
            if (complaintData != null) {
                // Log the chat initiation
                System.out.println("[Process: " + processInstanceId + "] Agent is following up with client after closure");
                
                // Set chat session variables
                execution.setVariable("chatSessionActive", true);
                execution.setVariable("chatInitiatedBy", "AGENT");
                execution.setVariable("chatReason", "POST_CLOSURE_FOLLOWUP");
                execution.setVariable("chatStartTime", System.currentTimeMillis());

                // TODO: Notify client about chat availability
                System.out.println("[Process: " + processInstanceId + "] Client would be notified about chat availability");

                System.out.println("[Process: " + processInstanceId + "] Agent chat session initiated successfully");

            } else {
                System.err.println("[Process: " + processInstanceId + "] No complaint data available for chat initiation");
                execution.setVariable("chatSessionActive", false);
            }

        } catch (Exception e) {
            System.err.println("[Process: " + processInstanceId + "] Error initiating agent chat: " + e.getMessage());
            e.printStackTrace();
            execution.setVariable("chatSessionActive", false);
        }
    }
}
