package bw.org.hrdf.account.models.company;

import bw.org.hrdf.account.entity.enums.OrganisationCategory;
import bw.org.hrdf.account.entity.enums.OrganisationTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class CompanySearchCriteria {
    private String bursTin;
    private String cipaNumber;
    private String bqaNumber;
    private String companyName;
    private String status;
    private OrganisationTypeEnum type;
    private String industry;
    private OrganisationCategory category;
    private String sortBy = "name";
    private String direction = "ASC";
    private String assignedTo;
    private String role;
}
