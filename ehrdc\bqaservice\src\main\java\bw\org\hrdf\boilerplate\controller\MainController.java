package bw.org.hrdf.boilerplate.controller;

import bw.org.hrdf.boilerplate.dto.CompanyDTO;
import bw.org.hrdf.boilerplate.entity.Company;
import bw.org.hrdf.boilerplate.helper.ApiResponse;
import bw.org.hrdf.boilerplate.models.Mapper;
import bw.org.hrdf.boilerplate.models.Requestor;
import bw.org.hrdf.boilerplate.services.CompanyService;
import jakarta.validation.Valid;
import jakarta.ws.rs.Path;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/v1/bqa")
public class MainController {

    private static final Logger logger = LoggerFactory.getLogger(MainController.class);

    @Autowired
    private CompanyService companyService = new CompanyService();

    @GetMapping("/{id}")
    public ResponseEntity<?> fetchCompanyById(@PathVariable String id) {
        try {
            logger.warn("Fetch company request initiated");
            Company company = companyService.fetchCompanyById(id);
            if (company == null) {
                return ResponseEntity.ok(new ApiResponse<>(false, "No record found", null, null));
            }
            Mapper mapper = new Mapper();
            CompanyDTO response = mapper.toCompanyDTO(company);


            return ResponseEntity.ok(new ApiResponse<>(true, "success", response, null));
        } catch (Exception e) {
            logger.error("Create new Otp failed with exception: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new ApiResponse<>(false, "Internal error occurred", null, List.of(
                            new ApiResponse.ErrorResponse(
                                    "INTERNAL_SERVER_ERROR", "Internal error occurred"
                            )
                    )));
        }
    }

        @GetMapping("/accreditation/{accreditationNo}")
        public ResponseEntity<?> fetchCompanyByAccreditationNo(@PathVariable String accreditationNo) {
            try {
                logger.warn("Fetching company by accreditation number: {}", accreditationNo);

                Company company = companyService.fetchAccreditationById(accreditationNo);

                if (company == null) {
                    return ResponseEntity.ok(new ApiResponse<>(false, "No record found", null, null));
                }

                Mapper mapper = new Mapper();
                CompanyDTO response = mapper.toCompanyDTO(company);

                return ResponseEntity.ok(new ApiResponse<>(true, "success", response, null));
            } catch (Exception e) {
                logger.error("Fetching company by accreditation number failed: {}", e.getMessage());
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                        .body(new ApiResponse<>(false, "Internal error occurred", null, List.of(
                                new ApiResponse.ErrorResponse("INTERNAL_SERVER_ERROR", "Internal error occurred")
                        )));
            }
        }


        @GetMapping("/{offset}/{limit}")
        public ResponseEntity<?> fetchAllCompanies() {
            try {
                logger.warn("Fetching all companies");

                List<Company> companies = companyService.fetchAllCompanies();
                if (companies.isEmpty()) {
                    return ResponseEntity.ok(new ApiResponse<>(false, "No records found", null, null));
                }

                Mapper mapper = new Mapper();
                List<CompanyDTO> response = companies.stream().map(mapper::toCompanyDTO).toList();

                return ResponseEntity.ok(new ApiResponse<>(true, "success", response, null));
            } catch (Exception e) {
                logger.error("Fetching all companies failed: {}", e.getMessage());
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                        .body(new ApiResponse<>(false, "Internal error occurred", null, List.of(
                                new ApiResponse.ErrorResponse("INTERNAL_SERVER_ERROR", "Internal error occurred")
                        )));
            }
        }

    }
