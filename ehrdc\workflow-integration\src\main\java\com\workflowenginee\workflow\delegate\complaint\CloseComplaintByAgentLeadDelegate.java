package com.workflowenginee.workflow.delegate.complaint;

import java.util.Map;

import org.flowable.engine.delegate.DelegateExecution;
import org.flowable.engine.delegate.JavaDelegate;
import org.springframework.stereotype.Component;

@Component("closeComplaintByAgentLeadDelegate")
public class CloseComplaintByAgentLeadDelegate implements JavaDelegate {

    @Override
    public void execute(DelegateExecution execution) {
        String processInstanceId = execution.getProcessInstanceId();
        String complaintId = (String) execution.getVariable("complaintId");
        String role = (String) execution.getVariable("role");

        System.out.println("[Process: " + processInstanceId + "] Agent Lead closing complaint: " + complaintId);

        try {
            Map<String, Object> escalatedComplaintData = (Map<String, Object>) execution.getVariable("escalatedComplaintData");
            
            if (escalatedComplaintData != null) {
                // Update complaint status to closed by agent lead
                System.out.println("[Process: " + processInstanceId + "] Updating complaint status to CLOSED by Agent Lead");
                
                execution.setVariable("complaintStatus", "CLOSED");
                execution.setVariable("closedBy", "AGENT_LEAD");
                execution.setVariable("closedAt", System.currentTimeMillis());
                execution.setVariable("escalationResolved", true);

                // TODO: Notify client about complaint closure by agent lead
                System.out.println("[Process: " + processInstanceId + "] Client would be notified about agent lead closure");

                // TODO: Notify original agent about resolution
                System.out.println("[Process: " + processInstanceId + "] Agent would be notified about escalation resolution");

                System.out.println("[Process: " + processInstanceId + "] Complaint closed successfully by agent lead");

            } else {
                System.err.println("[Process: " + processInstanceId + "] No escalated complaint data available for closure");
                execution.setVariable("complaintStatus", "ERROR");
            }

        } catch (Exception e) {
            System.err.println("[Process: " + processInstanceId + "] Error closing complaint by agent lead: " + e.getMessage());
            e.printStackTrace();
            execution.setVariable("complaintStatus", "ERROR");
        }
    }
}
