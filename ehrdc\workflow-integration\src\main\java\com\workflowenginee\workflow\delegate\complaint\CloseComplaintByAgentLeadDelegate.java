package com.workflowenginee.workflow.delegate.complaint;

import java.util.Map;

import org.flowable.engine.delegate.DelegateExecution;
import org.flowable.engine.delegate.JavaDelegate;
import org.springframework.stereotype.Component;

import com.workflowenginee.workflow.dto.NotifyToClientDto;
import com.workflowenginee.workflow.dto.NotifyUsersByRoleDto;
import com.workflowenginee.workflow.service.NotificationComplaintService;
import com.workflowenginee.workflow.util.Enums;

@Component("closeComplaintByAgentLeadDelegate")
public class CloseComplaintByAgentLeadDelegate implements JavaDelegate {

    private final NotificationComplaintService notificationComplaintService;

    public CloseComplaintByAgentLeadDelegate(NotificationComplaintService notificationComplaintService) {
        this.notificationComplaintService = notificationComplaintService;
    }

    @Override
    public void execute(DelegateExecution execution) {
        String processInstanceId = execution.getProcessInstanceId();
        String complaintId = (String) execution.getVariable("complaintId");
        String role = (String) execution.getVariable("role");

        System.out.println("[Process: " + processInstanceId + "] Agent Lead closing complaint: " + complaintId);

        try {
            Map<String, Object> escalatedComplaintData = (Map<String, Object>) execution.getVariable("escalatedComplaintData");
            
            if (escalatedComplaintData != null) {
                // Update complaint status to closed by agent lead
                System.out.println("[Process: " + processInstanceId + "] Updating complaint status to CLOSED by Agent Lead");
                
                execution.setVariable("complaintStatus", "CLOSED");
                execution.setVariable("closedBy", "AGENT_LEAD");
                execution.setVariable("closedAt", System.currentTimeMillis());
                execution.setVariable("escalationResolved", true);

                // Notify client about complaint closure by agent lead
                try {
                    NotifyToClientDto clientNotification = new NotifyToClientDto();
                    clientNotification.setApplicationType(Enums.ApplicationType.COMPLAINTS.name());
                    clientNotification.setApplicationId(complaintId);
                    clientNotification.setNotificationType(Enums.NotificationType.COMPLAINT_CLOSED.name());
                    clientNotification.setMessage("Your escalated complaint has been reviewed and resolved by our senior agent");

                    notificationComplaintService.notifyToClient(clientNotification);
                    
                    System.out.println("[Process: " + processInstanceId + "] Client notification sent for agent lead closure");
                    
                } catch (Exception notificationEx) {
                    System.err.println("[Process: " + processInstanceId + "] Failed to send client notification: " + notificationEx.getMessage());
                }

                // Notify original agent about resolution
                try {
                    NotifyUsersByRoleDto agentNotification = new NotifyUsersByRoleDto();
                    agentNotification.setApplicationType(Enums.ApplicationType.COMPLAINTS.name());
                    agentNotification.setApplicationId(complaintId);
                    agentNotification.setRole(Enums.Role.AGENT.name());
                    agentNotification.setNotificationType(Enums.NotificationType.ESCALATION_RESOLVED.name());
                    agentNotification.setMessage("The escalated complaint has been resolved by the agent lead");

                    notificationComplaintService.notifyUsersByRole(agentNotification);
                    
                    System.out.println("[Process: " + processInstanceId + "] Agent notified about escalation resolution");
                    
                } catch (Exception notificationEx) {
                    System.err.println("[Process: " + processInstanceId + "] Failed to notify agent: " + notificationEx.getMessage());
                }

                System.out.println("[Process: " + processInstanceId + "] Complaint closed successfully by agent lead");

            } else {
                System.err.println("[Process: " + processInstanceId + "] No escalated complaint data available for closure");
                execution.setVariable("complaintStatus", "ERROR");
            }

        } catch (Exception e) {
            System.err.println("[Process: " + processInstanceId + "] Error closing complaint by agent lead: " + e.getMessage());
            e.printStackTrace();
            execution.setVariable("complaintStatus", "ERROR");
        }
    }
}
