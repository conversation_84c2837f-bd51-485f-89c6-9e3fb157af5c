package bw.org.hrdf.boilerplate.utils;

import bw.org.hrdf.boilerplate.models.Requestor;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.kafka.common.serialization.Deserializer;

public class CustomRequestDeserializer implements Deserializer<Requestor> {
    private final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public Requestor deserialize(String topic, byte[] data) {
        try {
            return objectMapper.readValue(data, Requestor.class);
        } catch (Exception e) {
            throw new RuntimeException("Failed to deserialize JSON to OtpRequest", e);
        }
    }
}

