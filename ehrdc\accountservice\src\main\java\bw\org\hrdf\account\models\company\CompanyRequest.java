package bw.org.hrdf.account.models.company;

import bw.org.hrdf.account.entity.enums.OrganisationCategory;
import bw.org.hrdf.account.entity.enums.OrganisationTypeEnum;
import lombok.*;

import java.util.List;

@Data
@NoArgsConstructor @ToString @AllArgsConstructor @Builder
public class CompanyRequest {
    private String name;
    private OrganisationTypeEnum type;
    private String industry;
    private String physicalAddress;
    private String telephoneNumber;
    private String faxNumber;
    private OrganisationCategory category;
    private boolean pepPipStatus;
    private boolean pepPipAssociateStatus;
    private ContactPersonModel contactPerson;
    private List<EmployeeRequest> employees;
    private ApplicationRequest registrationApplication;
    private List<VerificationModel> verifications;
}
