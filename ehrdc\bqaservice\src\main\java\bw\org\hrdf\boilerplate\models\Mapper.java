package bw.org.hrdf.boilerplate.models;

import bw.org.hrdf.boilerplate.dto.CompanyDTO;
import bw.org.hrdf.boilerplate.dto.ProgrammesDTO;
import bw.org.hrdf.boilerplate.entity.Company;
import bw.org.hrdf.boilerplate.entity.CourseTypesEnum;
import bw.org.hrdf.boilerplate.entity.Programmes;

import java.util.HashSet;
import java.util.Set;
import java.util.stream.Collectors;

public class Mapper {

    public Company toCompanyEntity(CompanyDTO companyDTO) {
        Company company = new Company();
        company.setCompanyName(companyDTO.getCompanyName());
        company.setProgrammes(toProgrammeEntities(companyDTO.getProgrammes()));
        company.setAccreditationNo(companyDTO.getAccreditationNo());

        return company;
    }

    public Programmes toProgrammeEntity(ProgrammesDTO programmesDTO) {
        Programmes programmes = new Programmes();
        programmes.setProgrammeName(programmesDTO.getProgrammeName());
        programmes.setNoOfLearners(programmesDTO.getNoOfLearners());
        programmes.setAwardingBoard(programmesDTO.getAwardingBoard());
        programmes.setCourseType(CourseTypesEnum.valueOf(programmesDTO.getCourseType()));
        programmes.setDateOfAccreditation(programmesDTO.getDateOfAccreditation());
        programmes.setExpiryDate(programmesDTO.getExpiryDate());
        programmes.setModule(programmesDTO.getModule());

        return programmes;
    }

    public Set<Programmes> toProgrammeEntities(Set<ProgrammesDTO> programmesDTOS){
        return programmesDTOS.stream().map(this::toProgrammeEntity).collect(Collectors.toSet());
    }

    public ProgrammesDTO toProgrammesDTO(Programmes programmes) {
        ProgrammesDTO programmesDTO = new ProgrammesDTO();
        programmes.setProgrammeName(programmes.getProgrammeName());
        programmes.setNoOfLearners(programmes.getNoOfLearners());
        programmes.setAwardingBoard(programmes.getAwardingBoard());
        programmes.setCourseType(programmes.getCourseType());
        programmes.setDateOfAccreditation(programmes.getDateOfAccreditation());
        programmes.setExpiryDate(programmes.getExpiryDate());
        programmes.setModule(programmes.getModule());

        return programmesDTO;
    }

    public Set<ProgrammesDTO> toProgrammeDTOs(Set<Programmes> programmes) {
        return programmes.stream().map(this::toProgrammesDTO).collect(Collectors.toSet());
    }

    public CompanyDTO toCompanyDTO(Company company) {
        CompanyDTO companyDTO = new CompanyDTO();
        companyDTO.setCompanyName(company.getCompanyName());
        companyDTO.setAccreditationNo(company.getAccreditationNo());
        companyDTO.setProgrammes(toProgrammeDTOs(company.getProgrammes()));

        return companyDTO;
    }

    public Set<CompanyDTO> companyDTOS(Set<Company> companies) {
        return companies.stream().map(this::toCompanyDTO).collect(Collectors.toSet());
    }

}
