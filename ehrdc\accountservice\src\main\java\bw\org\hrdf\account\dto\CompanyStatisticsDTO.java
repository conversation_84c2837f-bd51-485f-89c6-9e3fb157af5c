package bw.org.hrdf.account.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @CreatedOn 22/03/25 17:46
 * @UpdatedBy martinspectre
 * @UpdatedOn 22/03/25 17:46
 */
@Data @AllArgsConstructor @NoArgsConstructor
public class CompanyStatisticsDTO {
    private Long totalRegistered;
    private Long activeCompanies;
    private Long pendingApprovals;
    private Long rejectedCompanies;
}
