package bw.org.hrdf.account;

import bw.org.hrdf.account.utils.DisableHostnameVerification;
import io.github.cdimascio.dotenv.Dotenv;
import io.swagger.v3.oas.annotations.OpenAPIDefinition;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;

@SpringBootApplication
@EnableJpaAuditing(auditorAwareRef = "auditAwareImpl")
@EnableFeignClients
@OpenAPIDefinition()
@EnableDiscoveryClient
public class AccountServiceApplication {
	private static final Logger logger = LoggerFactory.getLogger(AccountServiceApplication.class);

	public static void main(String[] args) {

		logger.info("Account Service Management initiated");

		Dotenv dotenv = Dotenv.configure().ignoreIfMissing().load();
		dotenv.entries().forEach(entry -> System.setProperty(entry.getKey(), entry.getValue()));

		//TODO Remove this in production
		// Disable hostname verification
		DisableHostnameVerification.disable();
		SpringApplication.run(AccountServiceApplication.class, args);
	}

}
