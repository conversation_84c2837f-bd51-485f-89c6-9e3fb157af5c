package bw.org.hrdf.account.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Set;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class CompanyDTO {
    private String uuid;
    private String name;
    private String type;
    private String industry;
    private String category;
    private String physicalAddress;
    private String telephoneNumber;
    private String faxNumber;
    private boolean pepPipStatus;
    private boolean pepPipAssociateStatus;

    private ContactPersonDTO contactPerson;
    private Set<EmployeeDTO> employees;
    private ApplicationDTO registrationApplication;
    private Set<VerificationDTO> verifications;
}
