package bw.org.hrdf.repositories;

import bw.org.hrdf.entity.UserSettings;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.Optional;

public interface UserSettingsRepository extends JpaRepository<UserSettings, Long> {

    Optional<UserSettings> findByUserId(String userId);

    @Query("SELECT COALESCE(u.is2FAEnabled, false) FROM UserSettings u WHERE u.userId = :userId")
    Boolean is2FAEnabled(String userId);
}
