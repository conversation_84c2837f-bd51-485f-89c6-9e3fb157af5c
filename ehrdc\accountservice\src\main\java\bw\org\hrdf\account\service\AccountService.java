package bw.org.hrdf.account.service;

import bw.org.hrdf.account.api.OtpClient;
import bw.org.hrdf.account.entity.UserCompany;
import bw.org.hrdf.account.entity.UserLoginPolicies;
import bw.org.hrdf.account.entity.enums.CommunicationType;
import bw.org.hrdf.account.entity.enums.OTPTypes;
import bw.org.hrdf.account.helper.ApiResponse;
import bw.org.hrdf.account.models.CommunicationRequest;
import bw.org.hrdf.account.models.otp.OtpRequest;
import bw.org.hrdf.account.models.authentication.GenericPasswordPolicy;
import bw.org.hrdf.account.models.user.UserRequest;
import bw.org.hrdf.account.repositories.LoginPolicyRepository;
import bw.org.hrdf.account.repositories.UserCompanyRepo;
import bw.org.hrdf.account.utils.PasswordUtil;
import jakarta.ws.rs.core.Response;
import org.keycloak.admin.client.Keycloak;
import org.keycloak.admin.client.resource.UserResource;
import org.keycloak.representations.idm.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * eHRDF Account Service
 * This service provides service to create, retrieve
 * update & delete account object in eHRDF.
 *
 * <AUTHOR> Ntlhe
 *
 */
@RefreshScope
@Service
public class AccountService {

    @Autowired
    private Keycloak keycloak;

    @Value("${keycloak.realm}")
    private String realm;

    @Value("${keycloak.client-id}")
    private String clientId;

    @Value("${keycloak.client-secret}")
    private String clientSecret;

    @Autowired
    private KafkaTemplate<String, OtpRequest> kafkaTemplate;

    @Value("${otp.topic.name}")
    private String otpTopic;

    @Autowired
    private UserCompanyRepo userCompanyRepoRepository;

    @Autowired
    private UserSettingsService settingsService;

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private OtpClient otpClient;

    @Autowired
    private KafkaTemplate<String, CommunicationRequest> communicationTemplate;

    @Autowired
    private LoginPolicyRepository loginPolicyRepository;

    private static final Logger logger = LoggerFactory.getLogger(AccountService.class);

    public UserRepresentation retrieveUserByUsername(String username) {
        return keycloak.realm(realm)
                .users()
                .search(username, 0, 1)
                .stream()
                .findFirst()
                .orElse(null);
    }

    public UserRepresentation retrieveUserByUserId(String userId){
        return keycloak.realm(realm).users().get(userId).toRepresentation();
    }

    public String retrieveCompanyIdByUserId(String userId){
        Optional<UserCompany> userCompanyData = userCompanyRepoRepository.findByUserId(userId);
        if(userCompanyData.isPresent()){
            UserCompany userCompany = userCompanyData.get();
            return userCompany.getCompanyId();
        }
        return "";
    }

    public String createUser(UserRequest userRequest, List<String> realmRoles, List<String> clientRoles) {
        UserRepresentation user = generateUser(userRequest);
        String userId;
        try (Response response = keycloak.realm(realm).users().create(user)) {
            if (response.getStatus() != 201) {
                throw new RuntimeException("Failed to create user");
            }
            userId = response.getLocation().getPath().replaceAll(".*/([^/]+)$", "$1");
            if(userRequest.getCompanyId() != null && !userRequest.getCompanyId().isEmpty()){
                UserCompany userCompany = new UserCompany();
                userCompany.setUserId(userId);
                userCompany.setCompanyId(userRequest.getCompanyId());

                userCompanyRepoRepository.save(userCompany);
            }
        }

        assignUserInitialRoles(userId, realmRoles, clientRoles);
        return userId;
    }

    private static UserRepresentation generateUser(UserRequest userRequest) {
        UserRepresentation user = new UserRepresentation();
        user.setUsername(userRequest.getUsername());
        user.setEmail(userRequest.getUsername());
        user.setEnabled(true);
        user.setEmailVerified(false);
        if(userRequest.getFirstName() != null && !userRequest.getFirstName().isEmpty()){
            user.setFirstName(userRequest.getFirstName());
        }
        if(userRequest.getLastName() != null && !userRequest.getLastName().isEmpty()){
            user.setLastName(userRequest.getLastName());
        }
        return user;
    }

    public void assignUserInitialRoles(String userId, List<String> realmRoles, List<String> clientRoles) {

        if (realmRoles != null) {
            for (String role : realmRoles) {
                RoleRepresentation realmRole = keycloak.realm(realm).roles().get(role).toRepresentation();
                keycloak.realm(realm).users().get(userId).roles().realmLevel().add(List.of(realmRole));
            }
        }

        if (clientRoles != null) {
            for (String clientRole : clientRoles) {
                ClientRepresentation clientResource = keycloak.realm(realm).clients().findByClientId(clientId).get(0);

                RoleRepresentation userClientRole = keycloak.realm(realm).clients().get(clientResource.getId())
                        .roles().get(clientRole).toRepresentation();

                keycloak.realm(realm).users().get(userId).roles().clientLevel(clientResource.getId()).add(List.of(userClientRole));
            }
        }
    }

    public void assignRoleToUser(String userId, List<String> clientRoles) {
        if (clientRoles != null) {
            for (String clientRole : clientRoles) {
                ClientRepresentation clientResource = keycloak.realm(realm).clients().findByClientId(clientId).get(0);

                RoleRepresentation userClientRole = keycloak.realm(realm).clients().get(clientResource.getId())
                        .roles().get(clientRole).toRepresentation();

                keycloak.realm(realm).users().get(userId).roles().clientLevel(clientResource.getId()).add(List.of(userClientRole));
            }
        }
    }
    public Map<String, List<String>> getUserRoles(String userId) {
        try {
            Map<String, List<String>> userRoles = new HashMap<>();
            List<RoleRepresentation> realmRoles = keycloak.realm(realm)
                    .users()
                    .get(userId)
                    .roles()
                    .realmLevel()
                    .listEffective();

            UserResource userResource = keycloak.realm(realm).users().get(userId);
            MappingsRepresentation userActiveRoles = userResource.roles().getAll();

            List<RoleRepresentation> realmMappings = userActiveRoles.getRealmMappings();
            Map<String, ClientMappingsRepresentation> clientMappings = userActiveRoles.getClientMappings();

            List<String> realmRoleNames = realmMappings.stream()
                    .map(role -> role.getName().replace("-", "_").toUpperCase())
                    .collect(Collectors.toList());
            userRoles.put("realmRoles", realmRoleNames);

            List<ClientRepresentation> clients = keycloak.realm(realm).clients().findByClientId(clientId);

            if(!clients.isEmpty()){
                for(ClientRepresentation client : clients){
                    List<RoleRepresentation> clientRoles = userResource.roles().clientLevel(client.getId()).listAll();
                    List<String> clientRoleNames = clientRoles.stream()
                            .map(role -> role.getName().replace("-", "_").toUpperCase())
                            .collect(Collectors.toList());
                    userRoles.put("roles", clientRoleNames);
                }
            }
            return userRoles;

        } catch (Exception e) {
            throw new RuntimeException("Failed to retrieve roles for user: " + userId, e);
        }
    }

    public String generatePassword(int passwordLength, int minLength, int maxLength, int requireUpperCaseCharacter, int requireLowerCaseCharacter, int requireDigit, int requireSpecialCharacter) {
        GenericPasswordPolicy<?> policy = new GenericPasswordPolicy<>()
                .setMinLength(minLength)
                .setMaxLength(maxLength)
                .setRequireUpperCaseCharacter(requireUpperCaseCharacter)
                .setRequireLowerCaseCharacter(requireLowerCaseCharacter)
                .setRequireDigit(requireDigit)
                .setRequireSpecialCharacter(requireSpecialCharacter);

        PasswordUtil passwordUtil = new PasswordUtil();
        return  passwordUtil.generatePassword(passwordLength, policy);
    }

    public String activateAccount(UserRepresentation user, OTPTypes otpType, Integer otpNumber){
        try {
            if (otpType.equals(OTPTypes.ACCOUNT_VERIFICATION)) {
                OtpRequest otpRequest = new OtpRequest();
                otpRequest.setUserId(user.getId());
                otpRequest.setOtpType(otpType);
                otpRequest.setOtp(otpNumber);
                otpRequest.setCommunicationType(CommunicationType.EMAIL);
                otpRequest.setContactAddress(user.getUsername());

                ApiResponse<?> apiResponse = otpClient.verifyOtp(otpRequest);
                if (apiResponse.isStatus()) {
                    user.setEmailVerified(true);
                    user.setEnabled(true);

                    keycloak.realm(realm).users().get(user.getId()).update(user);
                    String password = generatePassword(8, 6, 12, 1, 1, 1, 0);
                    System.out.println("password " + password);

                    CredentialRepresentation credentials = new CredentialRepresentation();
                    credentials.setTemporary(false);
                    credentials.setValue(password);

                    keycloak.realm(realm).users().get(user.getId()).resetPassword(credentials);

                    UserLoginPolicies loginPolicies = new UserLoginPolicies();
                    loginPolicies.setAccountLocked(false);
                    loginPolicies.setUsername(user.getUsername());
                    loginPolicies.setFirstTimeLogin(true);
                    loginPolicies.setFailedAttempts(0);
                    loginPolicies.setUpdatedAt(LocalDateTime.now());

                    loginPolicyRepository.save(loginPolicies);

                    //Trigger email communication to send account activation success with 1 time password
                    CommunicationRequest communicationRequest = new CommunicationRequest();

                    String body = "Account activated, your one time password is " + password;

                    communicationRequest.setUserId(user.getId());
                    communicationRequest.setContactAddress(user.getUsername());
                    communicationRequest.setCommunicationType(CommunicationType.EMAIL);
                    communicationRequest.setBody(body);

                    triggerNotification(communicationRequest, "account-activation");
                    return "ACCOUNT_VERIFIED_SUCCESSFULLY";
                } else {
                    return apiResponse.getErrors().get(0).getErrorCode();
                }
            }
            return "INVALID_OTP_TYPE";
        } catch (Exception e) {
            logger.error("Account activation of user {} for {} failed with exception: {}", user.getId(), otpType, e.getMessage());
            throw new RuntimeException("UNKNOWN_ERROR");
        }
    }

    public void triggerOtp(OtpRequest otpRequest) {
        logger.info("OTP request for user {} sent to Kafka topic {}", otpRequest.getUserId(), otpTopic);
        try {
            kafkaTemplate.send(otpTopic, otpRequest);
            logger.info("Otp request for user: {} is successful", otpRequest.getUserId());
        } catch (Exception e) {
            logger.error("Failed to send OTP request for user {} to Kafka: {}", otpRequest.getUserId(), e.getMessage(), e);
            throw new RuntimeException("Unable to process OTP request at this time.");
        }
    }

    public void triggerNotification(CommunicationRequest communicationRequest, String communicationTopic) {
        try {
            logger.info("Notification request for user {} to Kafka topic {} initiated", communicationRequest.getUserId(), communicationTopic);
            communicationTemplate.send(communicationTopic, communicationRequest);
            logger.info("Notification request for user {} to Kafka topic {} sent successfully", communicationRequest.getUserId(), communicationTopic);
        } catch (Exception e) {
            logger.error("Failed to send Notification request for user {} to Kafka: {}", communicationRequest.getUserId(), e.getMessage(), e);
            throw new RuntimeException("Unable to process Communication requests at this time.");
        }
    }

    public void createUserCompany(String userId, String companyId) {
        UserCompany userCompany = new UserCompany();
        userCompany.setUserId(userId);
        userCompany.setCompanyId(companyId);

        userCompanyRepoRepository.save(userCompany);
    }
}

