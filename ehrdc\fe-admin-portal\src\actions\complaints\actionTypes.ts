export enum ActionTypes {
    FETCH_COMPLAINTS_REQUESTS = 'FETCH_COMPLAINT_REQUESTS',
    FETCH_COMPLAINTS_REQUESTS_SUCCESS = 'FETCH_COMPLAINT_REQUESTS_SUCCESS',
    <PERSON>ET<PERSON>_COMPLAINTS_REQUESTS_FAILED = 'FETCH_COMPLAINT_REQUESTS_FAILED',

    FETCH_SELECTED_COMPLAINT_REQUESTS = 'FETCH_SELECTED_COMPLAINT_REQUESTS',
    FETCH_SELECTED_COMPLAINT_REQUESTS_SUCCESS = 'FETCH_SELECTED_COMPLAINT_REQUESTS_SUCCESS',
    FETCH_SELECTED_COMPLAINT_REQUESTS_FAILED = 'FETCH_SELECTED_COMPLAINT_REQUESTS_FAILED',

    SUBMIT_COMPLAINT_COMMENT_REQUEST = 'SUBMIT_COMPLAINT_COMMENT_REQUEST',
    SUBMIT_COMPLAINT_COMMENT_SUCCESS = 'SUBMIT_COMPLAINT_COMMENT_SUCCESS',
    SU<PERSON>IT_COMPLAINT_COMMENT_FAILED = 'SUBMIT_COMPLAINT_COMMENT_FAILED',

    UPLOAD_COMPLAINT_ATTACHMENT_REQUEST = 'UPLOAD_COMPLAINT_ATTACHMENT_REQUEST',
    UPLOAD_COMPLAINT_ATTACHMENT_SUCCESS = 'UPLOAD_COMPLAINT_ATTACHMENT_SUCCESS',
    UPLOAD_COMPLAINT_ATTACHMENT_FAILED = 'UPLOAD_COMPLAINT_ATTACHMENT_FAILED',
}
