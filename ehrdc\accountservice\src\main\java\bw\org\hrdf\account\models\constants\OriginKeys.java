/*
 * ******************************************************************************
 *       Cloud Foundry Copyright (c) [2009-2015] Pivotal Software, Inc. All Rights Reserved.
 *
 *       This product is licensed to you under the Apache License, Version 2.0 (the "License").
 *       You may not use this product except in compliance with the License.
 *
 *       This product includes a number of subcomponents with
 *       separate copyright notices and license terms. Your use of these
 *       subcomponents is subject to the terms and conditions of the
 *       subcomponent's license, as noted in the LICENSE file.
 * ******************************************************************************
 */

package bw.org.hrdf.account.models.constants;

public final class OriginKeys {

    private OriginKeys() {}
    public static final String NUMBER_CHARS = "**********";
    public static final String UPPER_CHARS = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
    public static final String LOWER_CHARS = "abcdefghijklmnopqrstuvwxyz";
    public static final String SPECIAL_CHARS = "#?!@$%^&*-";
    public static final String ALL_CHARS = NUMBER_CHARS + UPPER_CHARS + LOWER_CHARS + SPECIAL_CHARS;

}
