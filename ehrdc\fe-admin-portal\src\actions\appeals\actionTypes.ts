export enum ActionTypes {
    <PERSON>ETCH_APPEALS_REQUESTS = 'FETCH_APPEAL_REQUESTS',
    FETCH_APPEALS_REQUESTS_SUCCESS = 'FETCH_APPEAL_REQUESTS_SUCCESS',
    FETCH_APPEALS_REQUESTS_FAILED = 'FETCH_APPEAL_REQUESTS_FAILED',

    FETCH_SELECTED_APPEAL_REQUESTS = 'FETCH_SELECTED_APPEAL_REQUESTS',
    FETCH_SELECTED_APPEAL_REQUESTS_SUCCESS = 'FETCH_SELECTED_APPEAL_REQUESTS_SUCCESS',
    FETCH_SELECTED_APPEAL_REQUESTS_FAILED = 'FETCH_SELECTED_APPEAL_REQUESTS_FAILED',

    SUBMIT_APPEAL_COMMENT_REQUEST = 'SUBMIT_APPEAL_COMMENT_REQUEST',
    SU<PERSON><PERSON>_APPEAL_COMMENT_SUCCESS = 'SUBMIT_APPEAL_COMMENT_SUCCESS',
    SUBM<PERSON>_APPEAL_COMMENT_FAILED = 'SUBMIT_APPEAL_COMMENT_FAILED',

    UPLOAD_APPEAL_ATTACHMENT_REQUEST = 'UPLOAD_APPEAL_ATTACHMENT_REQUEST',
    UPLOAD_APPEAL_ATTACHMENT_SUCCESS = 'UPLOAD_APPEAL_ATTACHMENT_SUCCESS',
    UPLOAD_APPEAL_ATTACHMENT_FAILED = 'UPLOAD_APPEAL_ATTACHMENT_FAILED',
}
