package bw.org.hrdf.account.utils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Random;
import bw.org.hrdf.account.models.authentication.GenericPasswordPolicy;

import static bw.org.hrdf.account.models.constants.OriginKeys.*;

public class PasswordUtil {

    private final Random random = new Random();

    public String generatePassword(int passwordLength, GenericPasswordPolicy<?> policy) {
        if (!policy.allPresentAndPositive()) {
            throw new IllegalArgumentException("Password policy constraints are not properly set.");
        }

        // Ensure password length is within the policy constraints
        if (passwordLength < policy.getMinLength() || passwordLength > policy.getMaxLength()) {
            throw new IllegalArgumentException("Password length must be between " + policy.getMinLength() + " and " + policy.getMaxLength());
        }

        List<Character> password = new ArrayList<>();

        // Add at least one character from each required category (if required by the policy)
        addRequiredChars(password, policy);

        // Fill remaining password length with random characters from all categories
        for (int i = password.size(); i < passwordLength; i++) {
            password.add(ALL_CHARS.charAt(random.nextInt(ALL_CHARS.length())));
        }

        // Shuffle the password to ensure randomness
        Collections.shuffle(password);

        // Convert List<Character> to a String and return
        StringBuilder passwordStr = new StringBuilder(password.size());
        for (Character ch : password) {
            passwordStr.append(ch);
        }
        return passwordStr.toString();
    }

    private void addRequiredChars(List<Character> password, GenericPasswordPolicy<?> policy) {
        // Ensure we add required characters for each category if needed
        if (policy.getRequireUpperCaseCharacter() > 0) {
            password.add(UPPER_CHARS.charAt(random.nextInt(UPPER_CHARS.length())));
        }
        if (policy.getRequireLowerCaseCharacter() > 0) {
            password.add(LOWER_CHARS.charAt(random.nextInt(LOWER_CHARS.length())));
        }
        if (policy.getRequireDigit() > 0) {
            password.add(NUMBER_CHARS.charAt(random.nextInt(NUMBER_CHARS.length())));
        }
        if (policy.getRequireSpecialCharacter() > 0) {
            password.add(SPECIAL_CHARS.charAt(random.nextInt(SPECIAL_CHARS.length())));
        }
    }
}
