package bw.org.hrdf.account.service.company;

import bw.org.hrdf.account.entity.company.ContactPerson;
import bw.org.hrdf.account.repositories.company.ContactPersonRepository;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.transaction.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Optional;

@Service
public class ContactPersonService {

    @Autowired
    private ContactPersonRepository contactPersonRepository;
    @PersistenceContext
    private EntityManager entityManager;

    public ContactPerson save(Contact<PERSON>erson contactPerson) {
        return contactPersonRepository.save(contactPerson);
    }

    @Transactional()
    public String update(ContactPerson updatedContactPerson) {
        try {
            int updated = contactPersonRepository.updateContactPerson(
                    updatedContactPerson.getUuid(),
                    updatedContactPerson.getName(),
                    updatedContactPerson.getPosition(),
                    updatedContactPerson.getMobileNumber(),
                    updatedContactPerson.getEmail()
            );
            entityManager.flush();
            if (updated > 0) {
                return "SUCCESSFUL";
            }
            return "FAILED_UPDATE";
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public ContactPerson getContactPerson(String uuid) {
        Optional<ContactPerson> entity = contactPersonRepository.findByUuid(uuid);
        return entity.orElse(null);
    }
}

