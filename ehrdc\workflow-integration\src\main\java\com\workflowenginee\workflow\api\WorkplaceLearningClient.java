package com.workflowenginee.workflow.api;

import com.workflowenginee.workflow.config.FeignConfig;
import com.workflowenginee.workflow.util.ApiResponse;

import java.util.Map;
import java.util.UUID;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

@FeignClient(name = "WORKPLACE-LEARNING", fallback = WorkplaceLearningClientFallback.class, configuration = FeignConfig.class)
public interface WorkplaceLearningClient {
    @GetMapping("/api/v1/ncbsc/applications/recognition/{referenceNumber}")
    ApiResponse<?> getApplicationByReferenceNumber(@PathVariable String referenceNumber);

    @GetMapping("/api/v1/pre-approval-applications/application-number/{applicationId}")
    ApiResponse<?> getApplicationById(@PathVariable String applicationId);

    @GetMapping("/api/v1/ncbsc/workplace-training-plan/application-id/{id}")
    ApiResponse<?> getApplicationById(@PathVariable UUID id);

    @GetMapping("/api/v1/ncbsc/noc/reference-number/{referenceNumber}")
    ApiResponse<?> searchByReferenceNumber(@PathVariable String referenceNumber);

    @GetMapping("/api/v1/complaints/{id}")
    ApiResponse<?>  getComplaintById(@PathVariable String id);

    @PostMapping("/api/v1/complaints/{complaintId}/status")
    ApiResponse<?> updateComplaintStatus(@PathVariable("complaintId") String complaintId, @RequestBody Map<String, Object> statusUpdate);

    @PostMapping("/api/v1/complaints/{complaintId}/assign")
    ApiResponse<?> assignComplaint(@PathVariable("complaintId") String complaintId, @RequestBody Map<String, Object> assignmentData);

    @PostMapping("/api/v1/complaints/{complaintId}/escalate")
    ApiResponse<?> escalateComplaint(@PathVariable("complaintId") String complaintId, @RequestBody Map<String, Object> escalationData);

    @PostMapping("/api/v1/complaints/{complaintId}/close")
    ApiResponse<?> closeComplaint(@PathVariable("complaintId") String complaintId, @RequestBody Map<String, Object> closureData);

    @PostMapping("/api/v1/complaints/{complaintId}/chat")
    ApiResponse<?> initiateChatSession(@PathVariable("complaintId") String complaintId, @RequestBody Map<String, Object> chatData);
}
