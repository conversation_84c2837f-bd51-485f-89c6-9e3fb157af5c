package bw.org.hrdf.account.entity.company;

import bw.org.hrdf.account.entity.Auditable;
import bw.org.hrdf.account.entity.enums.StateEnum;
import bw.org.hrdf.account.entity.enums.StatusEnum;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonManagedReference;
import jakarta.persistence.*;
import lombok.*;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

@Entity
@Getter @Setter @AllArgsConstructor @NoArgsConstructor
@Table(name = "registration_applications")
public class ApplicationEntity extends Auditable implements Serializable {

    @Column(name = "reference_number", nullable = false)
    private String referenceNumber;

    @Column(nullable = false, name = "status")
    @Enumerated(EnumType.STRING)
    private StatusEnum status; // Example: Pending, Approved, Rejected, DRAFT

    @Column(nullable = false, name = "state")
    @Enumerated(EnumType.STRING)
    private StateEnum state; // Example: Submitted, in review

    @OneToOne
    @JoinColumn(name = "company_id", nullable = false)
    @ToString.Exclude
    @JsonIgnore
    private CompanyEntity company;

    @OneToMany(mappedBy = "registrationApplication", cascade = CascadeType.ALL, orphanRemoval = true, fetch = FetchType.LAZY)
    @JsonManagedReference
    @ToString.Exclude
    @JsonIgnore
    private List<Actions> actionsRequested;

    @Column(name = "application_submission_date")
    private LocalDateTime applicationSubmissionDate;

    @Column(name = "assigned_agent_lead")
    private String agentLead;

    @Column(name = "assigned_agent")
    private String agent;

    @Column(name = "assigned_manager")
    private String manager;

}
