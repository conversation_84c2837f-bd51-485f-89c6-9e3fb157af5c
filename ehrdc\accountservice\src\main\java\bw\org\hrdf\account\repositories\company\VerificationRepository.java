package bw.org.hrdf.account.repositories.company;

import bw.org.hrdf.account.entity.company.Verification;
import bw.org.hrdf.account.entity.enums.VerificationStatusEnum;
import bw.org.hrdf.account.entity.enums.VerificationTypeEnum;
import jakarta.transaction.Transactional;
import lombok.NonNull;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.Optional;

@Repository
public interface VerificationRepository extends JpaRepository<Verification, Long> {
    @Query("SELECT c FROM Verification c WHERE c.uuid = :uuid")
    @NonNull
    Optional<Verification> findByUuid(@NonNull String uuid);

    @Modifying
    @Transactional
    @Query("UPDATE Verification v SET " +
            "v.reference = :reference, " +
            "v.type = :type, " +
            "v.status = :status, " +
            "v.verifiedAt = :verifiedAt, " +
            "v.expiryDate = :expiryDate " +
            "WHERE v.uuid = :id")
    int updateVerification(String id, String reference,
                           VerificationTypeEnum type,
                           VerificationStatusEnum status,
                           LocalDateTime verifiedAt, Date expiryDate);
}

