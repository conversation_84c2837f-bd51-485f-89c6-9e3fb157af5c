package bw.org.hrdf.repositories;

import bw.org.hrdf.entity.UserLoginPolicies;
import jakarta.transaction.Transactional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.Set;

@Repository
public interface LoginPolicyRepository extends JpaRepository<UserLoginPolicies, Integer> {

    @Query("SELECT o FROM UserLoginPolicies o WHERE o.username = :username")
    Optional<UserLoginPolicies> findByUser(String username);

    @Modifying
    @Transactional
    @Query(value = "UPDATE user_login_policies SET is_first_time_login = false, failed_attempts = 0, account_locked = false WHERE updated_at IS NOT NULL AND updated_at + INTERVAL '60 minutes' <= CURRENT_TIMESTAMP", nativeQuery = true)
    int updateExpiredLoginAttempts();
}
