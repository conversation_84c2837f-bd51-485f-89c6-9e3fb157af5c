package bw.org.hrdf.account.entity.company;

import bw.org.hrdf.account.entity.Auditable;
import bw.org.hrdf.account.entity.enums.OrganisationCategory;
import bw.org.hrdf.account.entity.enums.OrganisationTypeEnum;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonManagedReference;
import jakarta.persistence.*;
import lombok.*;

import java.io.Serializable;
import java.util.Set;

/**
 * eHRDF Account Object representation
 * <AUTHOR>
 *
 */
@Entity
@Getter @Setter @AllArgsConstructor @NoArgsConstructor
@Table(name = "companies")
@NamedEntityGraph(name = "Company",
		attributeNodes = {
				@NamedAttributeNode("contactPerson"),
				@NamedAttributeNode("employees"),
				@NamedAttributeNode("registrationApplication"),
				@NamedAttributeNode("verifications")
		}
)
public class CompanyEntity extends Auditable implements Serializable {

	@Column(nullable = false, name = "name")
	private String name;

	@Column(nullable = false, name = "type")
	@Enumerated(EnumType.STRING)
	private OrganisationTypeEnum type;

	@Column(nullable = false, name = "industry")
	private String industry;

	@Column(nullable = false, name = "category")
	@Enumerated(EnumType.STRING)
	private OrganisationCategory category;

	@Column(name = "physical_address")
	private String physicalAddress;
	@Column(name = "telephone_number")
	private String telephoneNumber;
	@Column(name = "fax_number")
	private String faxNumber;

	@Column(nullable = false, name = "pep_pip_status")
	private boolean pepPipStatus;

	@Column(nullable = false, name = "pep_pip_association_status")
	private boolean pepPipAssociateStatus;

	@OneToOne(cascade = CascadeType.ALL, mappedBy = "company")
	@ToString.Exclude
	private ContactPerson contactPerson;

	@OneToMany(mappedBy = "company", cascade = CascadeType.ALL, orphanRemoval = true, fetch = FetchType.LAZY)
	@JsonManagedReference
	@ToString.Exclude
	private Set<Employee> employees;

	@OneToOne(cascade = CascadeType.ALL, mappedBy = "company")
	@ToString.Exclude
	private ApplicationEntity registrationApplication;

	@OneToMany(mappedBy = "company", cascade = CascadeType.ALL, orphanRemoval = true, fetch = FetchType.LAZY)
	@JsonManagedReference
	@ToString.Exclude
	private Set<Verification> verifications;

}
