# Complaint Lifecycle Workflow Integration

## Overview

This document describes the implementation of the complaint lifecycle workflow integration between the workplace-learning module and the workflow-integration module. The workflow automates the complaint handling process from initial submission through resolution.

## Architecture

### Components Created/Modified

#### 1. BPMN Workflow Definition
- **File**: `ehrdc/workflow-integration/src/main/resources/processes/complaint-lifecycle.bpmn20.xml`
- **Process ID**: `complaintLifecycleProcess`
- **Description**: Defines the complete complaint lifecycle flow with decision gateways and signal events

#### 2. Workflow Delegates
All delegates are located in `ehrdc/workflow-integration/src/main/java/com/workflowenginee/workflow/delegate/complaint/`:

- **FetchComplaintDelegate**: Fetches complaint details from workplace-learning service
- **NotifyAgentDelegate**: Sends notifications to agents about new complaints
- **AgentHandleComplaintDelegate**: Handles agent actions on complaints
- **CloseComplaintDelegate**: Processes complaint closure by agents
- **AgentChatAfterCloseDelegate**: Manages post-closure chat sessions
- **AgentChatWithClientDelegate**: Handles agent-client chat sessions
- **FetchAfterEscalationDelegate**: Fetches complaint data after escalation
- **CloseComplaintByAgentLeadDelegate**: Processes complaint closure by agent leads
- **ChatWithClientDelegate**: Manages agent lead-client chat sessions

#### 3. API Endpoints

##### Workflow Integration Module
- `POST /api/v1/workflow/start-complaint-lifecycle/{complaintId}`: Start complaint lifecycle process
- `POST /api/v1/workflow/resume-complaint-lifecycle/{processInstanceId}/{signalType}`: Resume workflow with signals

##### Workplace Learning Module
Updated ComplaintController with workflow integration:
- Complaint creation now starts the lifecycle workflow
- Escalation, closure, and communication actions trigger workflow signals

#### 4. Client Interfaces

##### WorkflowClient (workplace-learning)
```java
@PostMapping("/api/v1/workflow/start-complaint-lifecycle/{complaintId}")
Map<String, Object> startComplaintLifecycle(@PathVariable String complaintId);

@PostMapping("/api/v1/workflow/resume-complaint-lifecycle/{processInstanceId}/{signalType}")
Map<String, Object> resumeComplaintLifecycle(@PathVariable String processInstanceId, @PathVariable String signalType, Map<String, Object> workflowPayload);
```

##### WorkplaceLearningClient (workflow-integration)
```java
@PostMapping("/api/v1/complaints/{id}/status")
ApiResponse<?> updateComplaintStatus(@PathVariable String id, @RequestBody Map<String, Object> statusUpdate);

@PostMapping("/api/v1/complaints/{id}/comments")
ApiResponse<?> addComplaintComment(@PathVariable String id, @RequestBody Map<String, Object> comment);
```

## Workflow Process Flow

### 1. Complaint Submission
1. Client submits complaint through workplace-learning API
2. ComplaintController creates complaint entity
3. Workflow lifecycle process is automatically started
4. Process instance ID is saved to complaint entity

### 2. Agent Notification
1. FetchComplaintDelegate retrieves complaint details
2. NotifyAgentDelegate sends notification to available agents
3. Process waits for agent action

### 3. Agent Decision Points
Agents can take three actions:
- **Close**: Direct closure with optional follow-up chat
- **Chat**: Communicate with client before resolution
- **Escalate**: Escalate to agent lead

### 4. Escalation Flow
1. Complaint escalated to agent lead
2. FetchAfterEscalationDelegate retrieves updated data
3. Agent lead can either close or chat with client

### 5. Resolution
All paths lead to complaint resolution with appropriate notifications sent to all stakeholders.

## Signal Events

The workflow uses signal events for external interactions:

- **NotifyAgent**: Triggers agent notification
- **Escalated**: Handles escalation to agent lead
- **AgentDecision**: Processes agent decisions (close/chat/escalate)
- **EscalationDecision**: Processes agent lead decisions (close/chat)

## Notification System

### Enhanced DTOs
- **NotifyUsersByRoleDto**: Extended with notificationType and message fields
- **NotifyToClientDto**: Extended with notificationType and message fields

### Notification Types
Added new notification types to Enums:
- NEW_COMPLAINT
- COMPLAINT_CLOSED
- COMPLAINT_ESCALATED
- ESCALATION_RESOLVED
- AGENT_LEAD_INTERVENTION
- CHAT_AVAILABLE
- CHAT_STARTED

## Usage Examples

### Starting a Complaint Lifecycle
```bash
POST /api/v1/workflow/start-complaint-lifecycle/123e4567-e89b-12d3-a456-426614174000
```

### Escalating a Complaint
```bash
POST /api/v1/workflow/resume-complaint-lifecycle/process-123/Escalated
Content-Type: application/json

{
  "complaintId": "123e4567-e89b-12d3-a456-426614174000",
  "role": "Agent",
  "decision": "escalate"
}
```

### Agent Decision
```bash
POST /api/v1/workflow/resume-complaint-lifecycle/process-123/AgentDecision
Content-Type: application/json

{
  "complaintId": "123e4567-e89b-12d3-a456-426614174000",
  "role": "Agent",
  "decision": "close"
}
```

## Error Handling

### Fallback Mechanisms
- All Feign clients have fallback implementations
- Workflow delegates handle service unavailability gracefully
- Mock data is provided when external services fail

### Logging
Comprehensive logging is implemented at all levels:
- Process instance tracking
- Delegate execution logging
- Error logging with stack traces
- Notification delivery confirmation

## Testing

### Integration Testing
1. Start complaint lifecycle process
2. Verify process instance creation
3. Test agent notification delivery
4. Test decision signal handling
5. Verify escalation flow
6. Confirm resolution notifications

### Manual Testing Steps
1. Create a complaint through workplace-learning API
2. Verify workflow process starts automatically
3. Test agent actions (close/chat/escalate)
4. Verify notifications are sent correctly
5. Test escalation to agent lead
6. Confirm final resolution

## Configuration

### Required Environment Variables
- Kafka configuration for notifications
- Database configuration for workflow engine
- Service discovery configuration for Feign clients

### Dependencies
- Flowable workflow engine
- Spring Cloud OpenFeign
- Kafka for notifications
- PostgreSQL for workflow persistence

## Monitoring

### Process Monitoring
- Process instance status tracking
- Execution path monitoring
- Performance metrics collection

### Notification Monitoring
- Kafka message delivery tracking
- Notification success/failure rates
- User engagement metrics

## Future Enhancements

1. **SLA Monitoring**: Add time-based escalation rules
2. **Analytics Dashboard**: Complaint resolution metrics
3. **Client Portal Integration**: Real-time status updates
4. **Mobile Notifications**: Push notifications for mobile apps
5. **AI Integration**: Automated complaint categorization and routing
