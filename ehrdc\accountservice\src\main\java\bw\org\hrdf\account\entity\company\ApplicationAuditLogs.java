package bw.org.hrdf.account.entity.company;

import bw.org.hrdf.account.entity.Base;
import bw.org.hrdf.account.entity.enums.StatusEnum;
import com.fasterxml.jackson.annotation.JsonBackReference;
import jakarta.persistence.*;
import lombok.*;

@EqualsAndHashCode(callSuper = true)
@Entity
@Data @AllArgsConstructor
@Table(name = "application_audit_logs")
public class ApplicationAuditLogs extends Base {

    @ManyToOne
    @JoinColumn(name = "application_id", nullable = false)
    @JsonBackReference
    @ToString.Exclude
    private ApplicationEntity application;

    @Column(name = "performed_by", nullable = false)
    private String performedBy;

    @Column(nullable = false, name = "action")
    @Enumerated(EnumType.STRING)
    private StatusEnum action;

    @Column(nullable = false, name = "remarks")
    private String remarks;
}
