package bw.org.hrdf.boilerplate.entity;

import com.fasterxml.jackson.annotation.JsonManagedReference;
import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Set;

@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "mst_company")
@Data
public class Company extends Base{

    @Column(name="company_name", nullable = false)
    private String companyName;

    @Column(name="accreditation_number", nullable = false, unique = true)
    private String accreditationNo;

    @OneToMany(mappedBy = "company", fetch = FetchType.LAZY, cascade = CascadeType.ALL)
    @JsonManagedReference
    private Set<Programmes> programmes;


}
