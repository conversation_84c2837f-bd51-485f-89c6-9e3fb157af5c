package bw.org.hrdf.account.models.company;

import bw.org.hrdf.account.entity.enums.IDType;
import bw.org.hrdf.account.entity.enums.VerificationStatusEnum;
import lombok.*;

@Data
@NoArgsConstructor @ToString @AllArgsConstructor @Builder
public class EmployeeRequest {
    private String firstName;
    private String lastName;
    private String idNumber;
    private IDType idType;
    private VerificationStatusEnum idVerificationStatus;
    private String companyId;
}
