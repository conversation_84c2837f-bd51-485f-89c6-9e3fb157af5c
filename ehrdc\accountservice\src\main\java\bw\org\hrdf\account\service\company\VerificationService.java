package bw.org.hrdf.account.service.company;

import bw.org.hrdf.account.entity.company.Verification;
import bw.org.hrdf.account.repositories.company.VerificationRepository;
import jakarta.transaction.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

@Service
public class VerificationService{

    @Autowired
    private VerificationRepository repository;

    public Verification save(Verification verificationObject) {
        return repository.save(verificationObject);
    }

    @Transactional()
    public List<Verification> save(List<Verification> verificationObjects) {
        return repository.saveAllAndFlush(verificationObjects);
    }

    @Transactional()
    public void updateVerification(List<Verification> verificationObjects) {
        verificationObjects.forEach(object->repository.updateVerification(
                object.getUuid(),
                object.getReference(),
                object.getType(),
                object.getStatus(),
                object.getVerifiedAt(),
                object.getExpiryDate()
        ));
    }

    public Optional<Verification> getVerificationObject(String id) {
        return repository.findByUuid(id);
    }
}
