package bw.org.hrdf.account.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class VerificationDTO {
    private String uuid;
    private String reference;
    private String type;
    private String status;
    private LocalDateTime verifiedAt;
    private Date expiryDate;
}

