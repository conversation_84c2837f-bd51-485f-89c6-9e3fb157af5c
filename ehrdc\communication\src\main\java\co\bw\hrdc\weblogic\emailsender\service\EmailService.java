package co.bw.hrdc.weblogic.emailsender.service;
//import jakarta.mail.MessagingException;
//import jakarta.mail.internet.MimeMessage;
import co.bw.hrdc.weblogic.emailsender.dto.EmailRequest;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.jetbrains.annotations.NotNull;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Service;
import org.thymeleaf.TemplateEngine;
import org.thymeleaf.context.Context;

import javax.mail.MessagingException;
import javax.mail.internet.MimeMessage;

@Service
public class EmailService {

    private final JavaMailSender mailSender;
    private final TemplateEngine templateEngine;

    public EmailService(JavaMailSender mailSender, TemplateEngine templateEngine) {
        this.mailSender = mailSender;
        this.templateEngine = templateEngine;
    }

    public void sendEmail(String emailJson) {
        try {
            // Parse JSON message
            EmailRequest emailRequest = new ObjectMapper().readValue(emailJson, EmailRequest.class);

            MimeMessage message = mailSender.createMimeMessage();
            MimeMessageHelper helper = getMimeMessageHelper(message, emailRequest);

            Context context = new Context();
            context.setVariable("name", emailRequest.getName());
            context.setVariable("message", emailRequest.getBody());

            String htmlContent = templateEngine.process("email-template", context);
            helper.setText(htmlContent, true);

            mailSender.send(message);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @NotNull
    private static MimeMessageHelper getMimeMessageHelper(MimeMessage message, EmailRequest emailRequest) throws MessagingException {
        MimeMessageHelper helper = new MimeMessageHelper(message, true, "UTF-8");
        helper.setTo(emailRequest.getContactAddress());

        if(emailRequest.getOtpType() != null && !emailRequest.getOtpType().isEmpty()){
            if(emailRequest.getOtpType().equals("LOGIN_VERIFICATION")){
                helper.setSubject("Your 2fa password");
            }else if(emailRequest.getOtpType().equals("ACCOUNT_VERIFICATION")){
                helper.setSubject("Account Verification");
            }else{
                helper.setSubject("OTP Number");
            }
        }else{
            helper.setSubject("eHRDF Communication");
        }
        return helper;
    }
}
