package bw.org.hrdc.weblogic.workplacelearning.api;

import org.springframework.stereotype.Component;
import java.util.HashMap;
import java.util.Map;

@Component
public class WorkflowClientFallback implements WorkflowClient {
    @Override
    public Map<String, Object> getNCBSCApplication(String applicationType, String applicationNumber) {
        Map<String, Object> response = new HashMap<>();
        response.put("success", false);
        response.put("message", "Workflow service is currently unavailable");
        return response;
    }

     @Override
    public Map<String, Object> resumeProcess(String processId, String action, Map<String, Object> params) {
        Map<String, Object> response = new HashMap<>();
        response.put("success", false);
        response.put("message", "Workflow service is currently unavailable");
        return response;
    }
    
    @Override
    public Map<String, Object> getComplaintsWorkflow(String complaintId) {
        Map<String, Object> response = new HashMap<>();
        response.put("success", false);
        response.put("message", "Workflow service is currently unavailable");
        return response;
    }

    @Override
    public Map<String, Object> resumeComplaintProcess(String complaintId, String action, Map<String, Object> params) {
        Map<String, Object> response = new HashMap<>();
        response.put("success", false);
        response.put("message", "Workflow service is currently unavailable");
        return response;
    }

    @Override
    public Map<String, Object> startComplaintLifecycle(String complaintId) {
        Map<String, Object> response = new HashMap<>();
        response.put("success", false);
        response.put("message", "Workflow service is currently unavailable");
        return response;
    }

    @Override
    public Map<String, Object> resumeComplaintLifecycle(String processInstanceId, String signalType, Map<String, Object> workflowPayload) {
        Map<String, Object> response = new HashMap<>();
        response.put("success", false);
        response.put("message", "Workflow service is currently unavailable");
        return response;
    }
}