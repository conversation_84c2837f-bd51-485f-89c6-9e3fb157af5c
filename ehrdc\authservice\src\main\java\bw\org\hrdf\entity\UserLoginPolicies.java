package bw.org.hrdf.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.data.annotation.LastModifiedDate;

import java.time.LocalDateTime;

@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "user_login_policies")
@Data
public class UserLoginPolicies  extends Base{

    @Column
    private String username;

    @Column
    private boolean isFirstTimeLogin = true;

    @Column
    private Integer failedAttempts = 0;

    @Column
    private boolean accountLocked = false;

    @Column
    @LastModifiedDate
    private LocalDateTime updatedAt;
}
