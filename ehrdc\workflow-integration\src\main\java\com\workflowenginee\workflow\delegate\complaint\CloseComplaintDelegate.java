package com.workflowenginee.workflow.delegate.complaint;

import java.util.Map;

import org.flowable.engine.delegate.DelegateExecution;
import org.flowable.engine.delegate.JavaDelegate;
import org.springframework.stereotype.Component;

import com.workflowenginee.workflow.api.WorkplaceLearningClient;

@Component("closeComplaintDelegate")
public class CloseComplaintDelegate implements JavaDelegate {

    private final WorkplaceLearningClient workplaceLearningClient;

    public CloseComplaintDelegate(WorkplaceLearningClient workplaceLearningClient) {
        this.workplaceLearningClient = workplaceLearningClient;
    }

    @Override
    public void execute(DelegateExecution execution) {
        String processInstanceId = execution.getProcessInstanceId();
        String complaintId = (String) execution.getVariable("complaintId");
        String role = (String) execution.getVariable("role");

        System.out.println("[Process: " + processInstanceId + "] Agent closing complaint: " + complaintId);

        try {
            Map<String, Object> complaintData = (Map<String, Object>) execution.getVariable("complaintData");
            
            if (complaintData != null) {
                // Update complaint status to closed
                System.out.println("[Process: " + processInstanceId + "] Updating complaint status to CLOSED");
                
                // Here you would typically call an API to update the complaint status
                // For now, we'll just log the action
                execution.setVariable("complaintStatus", "CLOSED");
                execution.setVariable("closedBy", "AGENT");
                execution.setVariable("closedAt", System.currentTimeMillis());

                // TODO: Notify client about complaint closure
                System.out.println("[Process: " + processInstanceId + "] Client notification would be sent here");

                System.out.println("[Process: " + processInstanceId + "] Complaint closed successfully by agent");

            } else {
                System.err.println("[Process: " + processInstanceId + "] No complaint data available for closure");
                execution.setVariable("complaintStatus", "ERROR");
            }

        } catch (Exception e) {
            System.err.println("[Process: " + processInstanceId + "] Error closing complaint: " + e.getMessage());
            e.printStackTrace();
            execution.setVariable("complaintStatus", "ERROR");
        }
    }
}
