package com.workflowenginee.workflow.delegate.complaint;

import java.util.Map;

import org.flowable.engine.delegate.DelegateExecution;
import org.flowable.engine.delegate.JavaDelegate;
import org.springframework.stereotype.Component;

import com.workflowenginee.workflow.api.WorkplaceLearningClient;
import com.workflowenginee.workflow.dto.NotifyToClientDto;
import com.workflowenginee.workflow.service.NotificationComplaintService;
import com.workflowenginee.workflow.util.Enums;

@Component("closeComplaintDelegate")
public class CloseComplaintDelegate implements JavaDelegate {

    private final WorkplaceLearningClient workplaceLearningClient;
    private final NotificationComplaintService notificationComplaintService;

    public CloseComplaintDelegate(WorkplaceLearningClient workplaceLearningClient,
                                NotificationComplaintService notificationComplaintService) {
        this.workplaceLearningClient = workplaceLearningClient;
        this.notificationComplaintService = notificationComplaintService;
    }

    @Override
    public void execute(DelegateExecution execution) {
        String processInstanceId = execution.getProcessInstanceId();
        String complaintId = (String) execution.getVariable("complaintId");
        String role = (String) execution.getVariable("role");

        System.out.println("[Process: " + processInstanceId + "] Agent closing complaint: " + complaintId);

        try {
            Map<String, Object> complaintData = (Map<String, Object>) execution.getVariable("complaintData");
            
            if (complaintData != null) {
                // Update complaint status to closed
                System.out.println("[Process: " + processInstanceId + "] Updating complaint status to CLOSED");
                
                // Here you would typically call an API to update the complaint status
                // For now, we'll just log the action
                execution.setVariable("complaintStatus", "CLOSED");
                execution.setVariable("closedBy", "AGENT");
                execution.setVariable("closedAt", System.currentTimeMillis());

                // Notify client about complaint closure
                try {
                    NotifyToClientDto clientNotification = new NotifyToClientDto();
                    clientNotification.setApplicationType(Enums.ApplicationType.COMPLAINTS.name());
                    clientNotification.setApplicationId(complaintId);
                    clientNotification.setNotificationType(Enums.NotificationType.COMPLAINT_CLOSED.name());
                    clientNotification.setMessage("Your complaint has been resolved and closed by our agent");

                    notificationComplaintService.notifyToClient(clientNotification);
                    
                    System.out.println("[Process: " + processInstanceId + "] Client notification sent for complaint closure");
                    
                } catch (Exception notificationEx) {
                    System.err.println("[Process: " + processInstanceId + "] Failed to send client notification: " + notificationEx.getMessage());
                }

                System.out.println("[Process: " + processInstanceId + "] Complaint closed successfully by agent");

            } else {
                System.err.println("[Process: " + processInstanceId + "] No complaint data available for closure");
                execution.setVariable("complaintStatus", "ERROR");
            }

        } catch (Exception e) {
            System.err.println("[Process: " + processInstanceId + "] Error closing complaint: " + e.getMessage());
            e.printStackTrace();
            execution.setVariable("complaintStatus", "ERROR");
        }
    }
}
