import { forwardRef } from 'react';
import { useConfig } from '../ConfigProvider';
import type { CommonProps } from '../@types/common';
import type { ReactNode, ComponentPropsWithRef, MouseEvent } from 'react';

export interface CardProps
  extends CommonProps,
    Omit<ComponentPropsWithRef<'div'>, 'onClick'> {
  clickable?: boolean;
  bodyClass?: string;
  bordered?: boolean;
  header?: string | ReactNode;
  bodyText?: string | ReactNode;
  headerClass?: string;
  headerBorder?: boolean;
  headerExtra?: string | ReactNode;
  footer?: string | ReactNode;
  footerClass?: string;
  footerBorder?: boolean;
  icon?: ReactNode;
  onClick?: (e: MouseEvent<HTMLDivElement>) => void;
}

const Card = forwardRef<HTMLDivElement, CardProps>((props, ref) => {
  const { cardBordered } = useConfig();

  const {
    children,
    className,
    clickable = false,
    bodyClass,
    bordered = cardBordered || false,
    header,
    headerClass,
    headerBorder = true,
    headerExtra,
    bodyText,
    footer,
    footerClass,
    footerBorder = true,
    icon, // Destructure icon from props
    onClick,
    ...rest
  } = props;

  // Tailwind classes
  const cardClass = [
    'shadow-lg bg-white px-6 pt-6 pb-0 mx-2 bg-white rounded-xl size-48 basis-1/4',
    bordered ? 'shadow-md' : '',
    clickable ? 'cursor-pointer select-none' : '',
    className,
  ].join(' ');

  const cardBodyClass = ['mt-2', bodyClass].join(' ');

  const cardHeaderClass = [
    'flex items-center mb-2',
    headerBorder ? 'pb-2' : '',
    headerClass,
  ].join(' ');

  const cardFooterClass = [
    'text-sm text-blue-500 mt-2 pb-0 mb-0',
    footerBorder ? 'pt-0' : '',
    footerClass,
  ].join(' ');

  const renderHeader = () => {
    if (typeof header === 'string') {
      return <h4 className="text-lg font-medium">{header}</h4>;
    }
    return <>{header}</>;
  };

  const renderBodyText = () => {
    if (typeof bodyText === 'string') {
      return <h6 className="text-md font-medium">{bodyText}</h6>;
    }
    return <>{bodyText}</>;
  };

  const handleClick = (e: MouseEvent<HTMLDivElement>) => {
    onClick?.(e);
  };

  return (
    <div
      ref={ref}
      className={cardClass}
      role="presentation"
      onClick={handleClick}
      {...rest}
    >
      {header && (
        <div className={cardHeaderClass}>
          {renderHeader()}
          {headerExtra && <span className="ml-auto font-sans text-2xl">{headerExtra}</span>}
        </div>
      )}
      {icon && (
        <div className="icon-container bg-pink-500 rounded-full text-white font-bold p-3 inline-flex justify-center items-center mb-2">
            {icon}
        </div>
      )}
      <div className={cardBodyClass}>{children}</div>
      {bodyText && (
        <div className={'mt-4 font-sans'}>
          {renderBodyText()}
        </div>
      )}
      {footer && <div className={cardFooterClass}>{footer}</div>}
    </div>
  );
});

Card.displayName = 'Card';

export default Card;
