package bw.org.hrdf.account.entity.company;

import bw.org.hrdf.account.entity.Auditable;
import bw.org.hrdf.account.entity.enums.Gender;
import bw.org.hrdf.account.entity.enums.IDType;
import bw.org.hrdf.account.entity.enums.VerificationStatusEnum;
import com.fasterxml.jackson.annotation.JsonBackReference;
import jakarta.persistence.*;
import lombok.*;

import java.io.Serializable;

/**
 * eHRDF User Object representation
 * <AUTHOR>
 *
 */

@Entity
@Getter @Setter @AllArgsConstructor @NoArgsConstructor
@Table(name = "employees")

public class Employee extends Auditable implements Serializable {

	@Column(name = "first_name", nullable = false)
	private String firstName;

	@Column(name = "last_name", nullable = false)
	private String lastName;

	@Column(name = "id_number", nullable = false)
	private String idNumber;

	@Column(name = "id_type", nullable = false)
	private IDType idType;

	@Column(name = "gender")
	private Gender gender;

	@Column(name = "contact_number")
	private String contactNumber;

	@Column(name = "basic_salary")
	private String basicSalary;

	@Column(name = "id_verification_status", nullable = false)
	@Enumerated(EnumType.STRING)
	private VerificationStatusEnum idVerificationStatus;

	@ManyToOne(cascade = CascadeType.ALL, optional = false)
	@JoinColumn(name = "company_id")
	@JsonBackReference
	@ToString.Exclude
	private CompanyEntity company;

}