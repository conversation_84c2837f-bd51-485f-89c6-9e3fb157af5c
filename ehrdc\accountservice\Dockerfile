FROM maven:latest AS build

LABEL maintainer="<PERSON> <<EMAIL>>"

WORKDIR /app

COPY pom.xml ./
COPY src ./src

RUN mvn clean package -DskipTests

FROM openjdk:17-slim

WORKDIR /app
# Set environment variables
ENV SERVER_PORT=3451 \
    SPRING_PROFILES_ACTIVE=local

COPY --from=build /app/target/accountService-1.0.0.jar ./app.jar

EXPOSE 3451

ENTRYPOINT ["java", "-Dspring.profiles.active=${SPRING_PROFILES_ACTIVE}", "-jar", "./app.jar"]