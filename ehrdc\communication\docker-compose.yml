
version: '3.3'
services:
  messaging:
    image: "sims-messaging-qa:${sims_mesqa}"
    environment:
      - EUREKA_CLIENT_SERVICEURL_DEFAULTZONE=http://eureka:8761/eureka/
      - SPRING_PROFILES_ACTIVE=bitriQA
    container_name: sims-messaging-qa
    ports:
      - 8081:8081
    networks:
      - simsqa
    restart: always
    logging:
      options:
        max-size: "10m"
        max-file: "3"
    ulimits:
      core:
        hard: 2
        soft: 2
networks:
  simsqa:
