package bw.org.hrdf.account.entity.company;

import bw.org.hrdf.account.entity.Base;
import bw.org.hrdf.account.entity.enums.ActionsEnum;
import bw.org.hrdf.account.entity.enums.StatusEnum;
import com.fasterxml.jackson.annotation.JsonBackReference;
import jakarta.persistence.*;
import lombok.*;

import java.io.Serializable;

@Entity
@Getter @Setter @AllArgsConstructor @NoArgsConstructor
@Table(name = "actions_requested")
public class Actions extends Base implements Serializable {

    @Column(name = "type", nullable = false)
    @Enumerated(EnumType.STRING)
    private ActionsEnum type;

    @Column(name = "message")
    private String message;

    @Column(nullable = false, name = "status")
    @Enumerated(EnumType.STRING)
    private StatusEnum status; // Example: PENDING, ACTED_UPON

    @ManyToOne(cascade = CascadeType.ALL, optional = false)
    @JoinColumn(name = "registration_application_id", nullable = false)
    @JsonBackReference
    @ToString.Exclude
    private ApplicationEntity registrationApplication;
}
