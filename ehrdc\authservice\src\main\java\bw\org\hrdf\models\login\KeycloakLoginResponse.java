package bw.org.hrdf.models.login;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class KeycloakLoginResponse {
    @JsonProperty("access_token")
    String accessToken;
    @JsonProperty("expires_in")
    long expiresIn;
    @JsonProperty("refresh_expires_in")
    long refreshExpiresIn;
    @JsonProperty("refresh_token")
    String refreshToken;
    @JsonProperty("token_type")
    String tokenType;
    @JsonProperty("not-before-policy")
    long notBeforePolicy;
    @JsonProperty("session_state")
    String sessionState;
    String scope;
}
