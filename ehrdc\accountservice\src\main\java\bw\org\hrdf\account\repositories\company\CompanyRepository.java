package bw.org.hrdf.account.repositories.company;

import bw.org.hrdf.account.dto.CompanyStatisticsDTO;
import bw.org.hrdf.account.entity.company.CompanyEntity;
import bw.org.hrdf.account.entity.enums.OrganisationCategory;
import bw.org.hrdf.account.entity.enums.OrganisationTypeEnum;
import bw.org.hrdf.account.entity.enums.VerificationTypeEnum;
import lombok.NonNull;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.jpa.repository.*;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;
import java.util.Set;

@Repository
public interface CompanyRepository extends JpaRepository<CompanyEntity, Long>, JpaSpecificationExecutor<CompanyEntity> {

    @EntityGraph(value = "Company", type = EntityGraph.EntityGraphType.LOAD)
    @Query("SELECT c FROM CompanyEntity c")
    @NonNull
    Page<CompanyEntity> findAll(@NonNull Pageable pageable);

    @EntityGraph(value = "Company", type = EntityGraph.EntityGraphType.LOAD)
    @Query("SELECT c FROM CompanyEntity c WHERE c.uuid = :uuid")
    @NonNull
    Optional<CompanyEntity> findByUuid(@NonNull String uuid);

    @Modifying
    @Transactional
    @Query("UPDATE CompanyEntity c SET " +
            "c.name = :name, " +
            "c.type = :type, " +
            "c.industry = :industry, " +
            "c.category = :category, " +
            "c.updatedAt = CURRENT_TIMESTAMP, " +
            "c.physicalAddress = :physicalAddress, " +
            "c.telephoneNumber = :telephoneNumber, " +
            "c.faxNumber = :faxNumber, " +
            "c.pepPipStatus = :pepPipStatus, "+
            "c.pepPipAssociateStatus = :pepPipAssociateStatus " +
            "WHERE c.uuid = :id")
    int updateCompanyDetails(
            String id,
            String name,
            OrganisationTypeEnum type,
            String industry,
            OrganisationCategory category,
            String physicalAddress,
            String telephoneNumber,
            String faxNumber,
            boolean pepPipStatus,
            boolean pepPipAssociateStatus
    );

    @Modifying
    @Transactional
    @Query("UPDATE CompanyEntity c SET c.isDeleted = true WHERE c.uuid = :id")
    int deleteCompany(@Param("id") String id);

    @Query("SELECT c FROM CompanyEntity c " +
            "LEFT JOIN FETCH c.contactPerson " +
            "LEFT JOIN FETCH c.employees " +
            "LEFT JOIN FETCH c.registrationApplication " +
            "LEFT JOIN FETCH c.verifications")
    Page<CompanyEntity> findAllCompaniesWithRelationships(PageRequest pageable);

    @Query("SELECT o FROM CompanyEntity o")
    Page<CompanyEntity> findAllCompanies(Pageable pageable);


    @Query("SELECT o FROM CompanyEntity o " +
            "WHERE LOWER(o.name) LIKE LOWER(CONCAT('%', :name, '%'))")
    Page<CompanyEntity> findByName(String name, Pageable pageable);


    Page<CompanyEntity> findByCategory(OrganisationCategory category, Pageable pageable);

    Page<CompanyEntity> findByType(OrganisationTypeEnum type, Pageable pageable);

    @Query("SELECT o FROM CompanyEntity o JOIN o.verifications v " +
            "WHERE v.reference IN :references")
    Optional<CompanyEntity> findByVerificationReferences(List<String> references);


    @Query("SELECT o FROM CompanyEntity o JOIN o.verifications v " +
            "WHERE v.reference = :reference and v.type = :type")
    Optional<CompanyEntity> findByVerificationReference(String reference, VerificationTypeEnum type);

    @Query("SELECT o FROM CompanyEntity o " +
            "JOIN o.registrationApplication ra " +
            "WHERE ra.applicationSubmissionDate = :submissionDate")
    Page<CompanyEntity> findByApplicationSubmissionDate(LocalDate submissionDate, Pageable pageable);

    @Query("SELECT o FROM CompanyEntity o " +
            "JOIN o.registrationApplication ra " +
            "WHERE ra.status = :status")
    Page<CompanyEntity> findByApplicationStatus(String status, Pageable pageable);

    @Query("SELECT o FROM CompanyEntity o")
    Set<CompanyEntity> findAllNonPageableCompanies(Specification<CompanyEntity> spec);

    @Query(value = """
        SELECT\s
            COUNT(c.id) AS TOTAL_REGISTERED,
            COUNT(c.id) FILTER (WHERE ra.state IN ('SUBMITTED', 'APPROVAL') AND ra.status = 'APPROVED') AS ACTIVE_COMPANIES,
            COUNT(c.id) FILTER (WHERE ra.state NOT IN ('DRAFT') AND (ra.state IN ('SUBMITTED', 'APPROVAL') AND ra.status != 'APPROVED')) AS PENDING_APPROVALS,
            COUNT(c.id) FILTER (WHERE ra.status = 'REJECTED') AS REJECTED_COMPANIES
        FROM public.companies c
        INNER JOIN public.registration_applications ra ON c.uuid = ra.company_id
        WHERE c.deleted = false
       \s""", nativeQuery = true)
    List<Object[]>  getCompanyStatistics();

    @Query("SELECT o FROM CompanyEntity o " +
            "JOIN o.registrationApplication ra " +
            "WHERE ra.status = 'APPROVED' " +
            "AND (ra.state = 'APPROVAL' OR ra.state = 'SUBMITTED') " +
            "AND o.type IN ('EDUCATION_TRAINING_PROVIDER', 'EDUCATION_TRAINING_PROVIDER_LEVY_PAYER')")
    Page<CompanyEntity> fetchAllActiveEtps(Pageable pageable);

}
