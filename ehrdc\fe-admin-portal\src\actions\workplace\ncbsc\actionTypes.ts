export enum ActionTypes {
    /** NCBSC */
    FETCH_NCBSC_REQUESTS = 'FETCH_NCBSC_REQUESTS',
    FETCH_NCBSC_REQUESTS_SUCCESS = 'FETCH_NCBSC_REQUESTS_SUCCESS',
    FET<PERSON>_NCBSC_REQUESTS_FAILED = 'FETCH_NCBSC_REQUESTS_FAILED',

    /**NCBSC Recognition Application Stepper form actions */
    FETCH_CURRENT_STEP = 'FETCH_CURRENT_STEP',
    SET_CURRENT_STEP = 'SET_CURRENT_STEP',
    FETCH_STEP_STATUS = 'FETCH_STEP_STATUS',
    SET_STEP_STATUS = 'SET_STEP_STATUS',
    SET_NEXT_STEP="SET_NEXT_STEP",
    SET_PREVIOUS_STEP="SET_PREVIOUS_STEP",
    JUMP_TO_STEP='JUMP_TO_STEP',
    SET_FORM_DATA='SET_FORM_DATA',
    FETCH_FORM_DATA='FETCH_FORM_DATA',

}
