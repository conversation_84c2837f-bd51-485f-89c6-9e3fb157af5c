{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "type": "java",
            "name": "Current File",
            "request": "launch",
            "mainClass": "${file}"
        },
        {
            "type": "java",
            "name": "AccountServiceApplication",
            "request": "launch",
            "mainClass": "bw.org.hrdf.account.AccountServiceApplication",
            "projectName": "accountService"
        },
        {
            "type": "java",
            "name": "ApiGatewayApplication",
            "request": "launch",
            "mainClass": "bw.org.hrdc.weblogic.apigateway.ApiGatewayApplication",
            "projectName": "api-gateway"
        },
        {
            "type": "java",
            "name": "AuthServiceApplication",
            "request": "launch",
            "mainClass": "bw.org.hrdf.AuthServiceApplication",
            "projectName": "authService"
        },
        {
            "type": "java",
            "name": "DmsApplication",
            "request": "launch",
            "mainClass": "bw.org.hrdc.weblogic.dms.DmsApplication",
            "projectName": "dms"
        },
        {
            "type": "java",
            "name": "OtpServiceApplication",
            "request": "launch",
            "mainClass": "bw.org.hrdf.otp.OtpServiceApplication",
            "projectName": "otpService"
        },
        {
            "type": "java",
            "name": "ServiceDiscoveryApplication",
            "request": "launch",
            "mainClass": "bw.org.hrdc.weblogic.servicediscovery.ServiceDiscoveryApplication",
            "projectName": "service-discovery"
        },
        {
            "type": "java",
            "name": "WorkplaceLearningApplication",
            "request": "launch",
            "mainClass": "bw.org.hrdc.weblogic.workplacelearning.WorkplaceLearningApplication",
            "projectName": "workplace-learning"
        }
    ]
}