package bw.org.hrdf.account.controller;

import bw.org.hrdf.account.entity.UserSettings;
import bw.org.hrdf.account.models.user.SettingsRequest;
import bw.org.hrdf.account.service.UserSettingsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/v1/user/settings")
public class UserSettingsController {

    @Autowired
    private UserSettingsService userSettingsService;

    @GetMapping("/{userId}")
    public ResponseEntity<UserSettings> getUserSettings(@PathVariable String userId) {
        return ResponseEntity.ok(userSettingsService.getUserSettings(userId));
    }

    @PutMapping("/{userId}/2fa")
    public ResponseEntity<String> updateUserSettings(@PathVariable String userId, @RequestBody SettingsRequest settings) {
        UserSettings updatedSettings = userSettingsService.updateUserSettings(userId,
                settings.getMethod(), settings.getPhoneNumber(), settings.getBackupEmail());
        //TODO format response like the other responses
        return ResponseEntity.ok("Two-Factor Authentication enabled for user: " + userId);
    }
}

