package com.workflowenginee.workflow.delegate.complaint;

import java.util.Map;

import org.flowable.engine.delegate.DelegateExecution;
import org.flowable.engine.delegate.JavaDelegate;
import org.springframework.stereotype.Component;

import com.workflowenginee.workflow.dto.NotifyToClientDto;
import com.workflowenginee.workflow.dto.NotifyUsersByRoleDto;
import com.workflowenginee.workflow.service.NotificationComplaintService;
import com.workflowenginee.workflow.util.Enums;

@Component("chatWithClientDelegate")
public class ChatWithClientDelegate implements JavaDelegate {

    private final NotificationComplaintService notificationComplaintService;

    public ChatWithClientDelegate(NotificationComplaintService notificationComplaintService) {
        this.notificationComplaintService = notificationComplaintService;
    }

    @Override
    public void execute(DelegateExecution execution) {
        String processInstanceId = execution.getProcessInstanceId();
        String complaintId = (String) execution.getVariable("complaintId");
        String role = (String) execution.getVariable("role");

        System.out.println("[Process: " + processInstanceId + "] Agent Lead initiating chat with client for complaint: " + complaintId);

        try {
            Map<String, Object> escalatedComplaintData = (Map<String, Object>) execution.getVariable("escalatedComplaintData");
            
            if (escalatedComplaintData != null) {
                // Log the chat initiation by agent lead
                System.out.println("[Process: " + processInstanceId + "] Agent Lead is starting chat session with client");
                
                // Set chat session variables
                execution.setVariable("chatSessionActive", true);
                execution.setVariable("chatInitiatedBy", "AGENT_LEAD");
                execution.setVariable("chatReason", "ESCALATED_COMPLAINT_DISCUSSION");
                execution.setVariable("chatStartTime", System.currentTimeMillis());

                // Notify client about chat session with agent lead
                try {
                    NotifyToClientDto clientNotification = new NotifyToClientDto();
                    clientNotification.setApplicationType(Enums.ApplicationType.COMPLAINTS.name());
                    clientNotification.setApplicationId(complaintId);
                    clientNotification.setNotificationType(Enums.NotificationType.CHAT_STARTED.name());
                    clientNotification.setMessage("Our senior agent has started a chat session to discuss your escalated complaint");

                    notificationComplaintService.notifyToClient(clientNotification);
                    
                    System.out.println("[Process: " + processInstanceId + "] Client notified about agent lead chat session");
                    
                } catch (Exception notificationEx) {
                    System.err.println("[Process: " + processInstanceId + "] Failed to send chat notification: " + notificationEx.getMessage());
                }

                // Notify original agent about agent lead intervention
                try {
                    NotifyUsersByRoleDto agentNotification = new NotifyUsersByRoleDto();
                    agentNotification.setApplicationType(Enums.ApplicationType.COMPLAINTS.name());
                    agentNotification.setApplicationId(complaintId);
                    agentNotification.setRole(Enums.Role.AGENT.name());
                    agentNotification.setNotificationType(Enums.NotificationType.AGENT_LEAD_INTERVENTION.name());
                    agentNotification.setMessage("Agent lead is handling the escalated complaint through direct client communication");

                    notificationComplaintService.notifyUsersByRole(agentNotification);
                    
                    System.out.println("[Process: " + processInstanceId + "] Agent notified about agent lead intervention");
                    
                } catch (Exception notificationEx) {
                    System.err.println("[Process: " + processInstanceId + "] Failed to notify agent: " + notificationEx.getMessage());
                }

                // After chat completion, mark complaint as resolved
                execution.setVariable("complaintStatus", "RESOLVED_VIA_AGENT_LEAD_CHAT");
                execution.setVariable("resolvedBy", "AGENT_LEAD");
                execution.setVariable("resolvedAt", System.currentTimeMillis());
                execution.setVariable("escalationResolved", true);

                System.out.println("[Process: " + processInstanceId + "] Agent lead chat session completed successfully");

            } else {
                System.err.println("[Process: " + processInstanceId + "] No escalated complaint data available for chat");
                execution.setVariable("chatSessionActive", false);
            }

        } catch (Exception e) {
            System.err.println("[Process: " + processInstanceId + "] Error in agent lead chat: " + e.getMessage());
            e.printStackTrace();
            execution.setVariable("chatSessionActive", false);
        }
    }
}
