package bw.org.hrdf.models.login;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.sql.Timestamp;
import java.util.List;
import java.util.Map;

@Data
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@JsonAutoDetect(fieldVisibility = Visibility.ANY, getterVisibility = Visibility.NONE, setterVisibility = Visibility.NONE)
@Schema(
        name = "AuthenticationResponse",
        description = "Schema to hold authentication response details"
)
public class AuthenticationResponse {
    private String accessToken;
    private String idToken;
    private String refreshToken;
    private Long expiresIn;
    private String userId;
    private Map<String, List<String>> roles;
    private String companyId;
    private String firstName;
    private String lastName;
    private boolean isOTPRequired;
    private String phoneNumber;
    private boolean firstTimeLogin;
    private Timestamp lockStartTime;
    private boolean isEmailVerified;
    private String createdDate;
    private boolean requiresPasswordReset;
}
