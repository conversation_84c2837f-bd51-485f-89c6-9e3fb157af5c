package bw.org.hrdf.controller;

import bw.org.hrdf.api.CompanyClient;
import bw.org.hrdf.api.OtpClient;
import bw.org.hrdf.entity.UserLoginPolicies;
import bw.org.hrdf.entity.UserSettings;
import bw.org.hrdf.entity.enums.CommunicationType;
import bw.org.hrdf.entity.enums.OTPTypes;
import bw.org.hrdf.helper.ApiResponse;
import bw.org.hrdf.models.login.AuthenticationResponse;
import bw.org.hrdf.models.login.AutologinRequest;
import bw.org.hrdf.models.login.KeycloakLoginResponse;
import bw.org.hrdf.models.login.TokenRequestBody;
import bw.org.hrdf.models.otp.OtpRequest;
import bw.org.hrdf.service.AuthService;
import bw.org.hrdf.service.UserSettingsService;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import org.keycloak.representations.idm.UserRepresentation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.*;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.HttpClientErrorException;

import java.sql.Timestamp;
import java.util.*;

@RestController
@RequestMapping("/api/v1/oauth2")

public class AuthController {

    private static final Logger logger = LoggerFactory.getLogger(AuthController.class);

    @Autowired
    private AuthService authService;
    @Autowired
    private UserSettingsService userSettingsService;

    @Autowired
    private OtpClient otpClient;

    @Autowired
    private CompanyClient companyClient;

    @GetMapping("/verify/{username}")
    public ResponseEntity<?> checkUsername(@Valid @PathVariable String username) {
        try {
            AuthenticationResponse loginResponse = new AuthenticationResponse();
            UserRepresentation userDetails = authService.getUserByUsername(username);
            if (userDetails == null) {
                return ResponseEntity.status(HttpStatus.OK)
                        .body(new ApiResponse<>(false, "Invalid username", null,
                                List.of(new ApiResponse.ErrorResponse("INVALID_USERNAME", "Invalid username"))));
            }
            loginResponse.setUserId(userDetails.getId());
            UserLoginPolicies loginPolicies = userSettingsService.isAccountLocked(username);
            if(loginPolicies != null && loginPolicies.isAccountLocked()){
                loginResponse.setLockStartTime(Timestamp.valueOf(loginPolicies.getUpdatedAt()));
                return ResponseEntity.status(HttpStatus.OK)
                        .body(new ApiResponse<>(false, "Login attempts exceeded", loginResponse, List.of(new ApiResponse.ErrorResponse("ATTEMPTS_EXCEEDED", "Maximum window login attempts exceeded"))));
            }

            boolean isEmailVerified = Boolean.TRUE.equals(userDetails.isEmailVerified());
            if(!isEmailVerified){
                loginResponse.setEmailVerified(false);
                return ResponseEntity.status(HttpStatus.OK)
                        .body(new ApiResponse<>(false, "Account not activated", loginResponse, List.of(new ApiResponse.ErrorResponse("REQUESTED_TO_ACTIVATE_ACCOUNT_KINDLY_CHANGE", "Account not active"))));
            }

            return ResponseEntity.ok(new ApiResponse<>(true, "user exist", loginResponse, null));

        } catch (Exception e) {
            logger.error("Username check failed with exception: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new ApiResponse<>(false, "Internal error occurred", null, List.of(new ApiResponse.ErrorResponse("INTERNAL_SERVER_ERROR", "Internal error occurred"))));
        }
    }


    //TODO To add logic for session control, if session exist request termination before log in.
    @PostMapping("/authorize")
    public ResponseEntity<?> login(@Valid @RequestBody AutologinRequest loginRequest) {
        try {
            UserRepresentation userDetails = authService.getUserByUsername(loginRequest.getUsername());
            AuthenticationResponse loginResponse = new AuthenticationResponse();
            if (userDetails == null) {
                userSettingsService.updateLoginAttempts(loginRequest.getUsername(), true);
                return ResponseEntity.status(HttpStatus.OK)
                        .body(new ApiResponse<>(false, "Invalid username or password", null,
                                List.of(new ApiResponse.ErrorResponse("INVALID_CREDENTIALS", "Invalid username or password"))));
            }
            UserLoginPolicies loginPolicies = userSettingsService.isAccountLocked(loginRequest.getUsername());
            if(loginPolicies != null && loginPolicies.isAccountLocked()){
                Map<String, Object> response = new HashMap<>();
                response.put("lockStartTime", loginPolicies.getUpdatedAt());
                return ResponseEntity.status(HttpStatus.OK)
                        .body(new ApiResponse<>(false, "Login attempts exceeded", response, List.of(new ApiResponse.ErrorResponse("ATTEMPTS_EXCEEDED", "Maximum window login attempts exceeded"))));
            }

            ResponseEntity<String> response =  authService.authenticateUser(loginRequest.getUsername(), loginRequest.getPassword());

            if (response.getStatusCode() == HttpStatus.OK) {

                boolean isEmailVerified = Boolean.TRUE.equals(userDetails.isEmailVerified());
                loginResponse.setEmailVerified(isEmailVerified);
                if(!isEmailVerified){
                    OtpRequest otpRequest = authService.getOtpRequest(loginRequest, userDetails, OTPTypes.ACCOUNT_VERIFICATION);
                    otpClient.generateOtp(otpRequest);
                }

                boolean is2FAEnabled = userSettingsService.is2FAEnabled(userDetails.getId());
                loginResponse.setOTPRequired(is2FAEnabled);
                if (is2FAEnabled) {
                    OtpRequest otpRequest = authService.getOtpRequest(loginRequest, userDetails, OTPTypes.LOGIN_VERIFICATION);
                    otpClient.generateOtp(otpRequest);
                }

                ApiResponse<?> apiResponse = companyClient.fetchCompanyIdentifier(userDetails.getId());
                if (apiResponse.isStatus()) {
                    loginResponse.setCompanyId(apiResponse.getData().toString());
                }

                Map<String, List<String>> userRoles = authService.getUserRoles(userDetails.getId());

                String responseBody = response.getBody();
                ObjectMapper objectMapper = new ObjectMapper();
                KeycloakLoginResponse keycloakLoginResponse = objectMapper.readValue(responseBody, KeycloakLoginResponse.class);
                loginResponse.setAccessToken(keycloakLoginResponse.getAccessToken());
                loginResponse.setIdToken(keycloakLoginResponse.getSessionState());
                loginResponse.setRefreshToken(keycloakLoginResponse.getRefreshToken());
                loginResponse.setExpiresIn(keycloakLoginResponse.getExpiresIn());
                loginResponse.setUserId(userDetails.getId());
                loginResponse.setRoles(userRoles);

                loginResponse.setFirstName(userDetails.getFirstName());
                loginResponse.setLastName(userDetails.getLastName());
                boolean isFirstTimeLogin = userSettingsService.isFirstTimeLogin(loginRequest.getUsername());
                loginResponse.setFirstTimeLogin(isFirstTimeLogin);
                loginResponse.setRequiresPasswordReset(isFirstTimeLogin);

                return ResponseEntity.ok(new ApiResponse<>(true, "Log in successful", loginResponse, null));
            } else {
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                        .body(new ApiResponse<>(false, "Unexpected response", null, List.of(new ApiResponse.ErrorResponse("INTERNAL_SERVER_ERROR", "Unexpected response occurred"))));
            }
        } catch (HttpClientErrorException e) {
            logger.error("Account sign in failed with keycloak exception: {}", e.getMessage());
            if (e.getStatusCode() == HttpStatus.UNAUTHORIZED) {
                userSettingsService.updateLoginAttempts(loginRequest.getUsername(), true);
                return ResponseEntity.status(HttpStatus.OK)
                        .body(new ApiResponse<>(false, "Invalid username or password", null, List.of(new ApiResponse.ErrorResponse("INVALID_CREDENTIALS", "Invalid username or password"))));
            } else if (e.getStatusCode() == HttpStatus.NOT_FOUND) {
                userSettingsService.updateLoginAttempts(loginRequest.getUsername(), true);
                return ResponseEntity.status(HttpStatus.OK)
                        .body(new ApiResponse<>(false, "Invalid username or password", null, List.of(new ApiResponse.ErrorResponse("NOT_FOUND", "Provided credential do not exist or they are wrong"))));
            } else {
                return ResponseEntity.status(HttpStatus.OK)
                        .body(new ApiResponse<>(false, "Unknown error", null, List.of(new ApiResponse.ErrorResponse("UNKNOWN_ERROR", "An error occurred, thats all we know."))));
            }
        } catch (Exception e) {
            String message = e.getMessage().equals("MAXIMUM_REQUEST_REACHED") ? "Otp maximum request reached" : e.getMessage();
            logger.error("Account sign in failed with exception: {}", message);
            if(e.getMessage().equals("MAXIMUM_REQUEST_REACHED")){
                return ResponseEntity.status(HttpStatus.OK)
                        .body(new ApiResponse<>(false, "Otp maximum request reached", null, null));
            }

            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new ApiResponse<>(false, "Internal error occurred", null, List.of(new ApiResponse.ErrorResponse("INTERNAL_SERVER_ERROR", "Internal error occurred"))));
        }
    }

    @PostMapping("/users/{userId}/reset-password")
    public ResponseEntity<?> resetPassword(@PathVariable String userId, @RequestBody Map<String, Object> newPassword) {
        try {
            UserRepresentation userDetails = authService.resetPassword(userId, newPassword.get("password").toString());
            userSettingsService.updateFirstTimeLogin(userDetails.getUsername());
            return ResponseEntity.status(HttpStatus.OK)
                    .body(new ApiResponse<>(true, "Password reset successful", null, null));

        } catch (Exception e) {
            logger.error("Password change failed with exception: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new ApiResponse<>(false, "Internal error occurred", null, List.of(new ApiResponse.ErrorResponse("INTERNAL_SERVER_ERROR", "Internal error occurred"))));
        }
    }

    @PutMapping("/authorize/multi-factor")
    public ResponseEntity<?> verification2fa(@RequestBody Map<String, Object> loginRequest) {
        try {
            String username = loginRequest.get("username").toString();
            String password = loginRequest.get("password").toString();

            OtpRequest otpRequest = new OtpRequest();
            UserRepresentation userDetails = authService.getUserByUsername(username);;
            if(loginRequest.get("userId") != null){
                otpRequest.setUserId(loginRequest.get("userId").toString());
            }else{
                if(userDetails != null){
                    otpRequest.setUserId(userDetails.getId());
                }
            }

            otpRequest.setOtpType(OTPTypes.LOGIN_VERIFICATION);
            otpRequest.setOtp(Integer.valueOf(loginRequest.get("otpNumber").toString()));
            otpRequest.setCommunicationType(CommunicationType.valueOf(loginRequest.get("communicationType").toString()));
            otpRequest.setContactAddress(username);

            ApiResponse<?> apiResponse = otpClient.verifyOtp(otpRequest);
            if (apiResponse.isStatus()) {
                userSettingsService.updateLoginAttempts(username, false);

                AuthenticationResponse loginResponse = new AuthenticationResponse();

                ResponseEntity<String> response =  authService.authenticateUser(username, password);

                if (response.getStatusCode() == HttpStatus.OK) {

                    loginResponse.setEmailVerified(true);
                    loginResponse.setOTPRequired(false);

                    assert userDetails != null;
                    ApiResponse<?> apiCompanyResponse = companyClient.fetchCompanyIdentifier(userDetails.getId());
                    if (apiCompanyResponse.isStatus()) {
                        loginResponse.setCompanyId(apiCompanyResponse.getData().toString());
                    }

                    Map<String, List<String>> userRoles = authService.getUserRoles(userDetails.getId());

                    String responseBody = response.getBody();
                    ObjectMapper objectMapper = new ObjectMapper();
                    KeycloakLoginResponse keycloakLoginResponse = objectMapper.readValue(responseBody, KeycloakLoginResponse.class);
                    loginResponse.setAccessToken(keycloakLoginResponse.getAccessToken());
                    loginResponse.setIdToken(keycloakLoginResponse.getSessionState());
                    loginResponse.setRefreshToken(keycloakLoginResponse.getRefreshToken());
                    loginResponse.setExpiresIn(keycloakLoginResponse.getExpiresIn());
                    loginResponse.setUserId(userDetails.getId());
                    loginResponse.setRoles(userRoles);

                    loginResponse.setFirstName(userDetails.getFirstName());
                    loginResponse.setLastName(userDetails.getLastName());
                    loginResponse.setFirstTimeLogin(false);
                    loginResponse.setRequiresPasswordReset(false);

                    return ResponseEntity.ok(new ApiResponse<>(true, "Log in successful", loginResponse, null));
                } else {
                    return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                            .body(new ApiResponse<>(false, "Unexpected response", null, List.of(new ApiResponse.ErrorResponse("INTERNAL_SERVER_ERROR", "Unexpected response occurred"))));
                }
            } else {
                return ResponseEntity.status(HttpStatus.OK)
                        .body(new ApiResponse<>(false, "Unexpected response", null, apiResponse.getErrors()));
            }
        } catch (HttpClientErrorException e) {
            logger.error("2fa Account sign in failed with keycloak exception: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.OK)
                        .body(new ApiResponse<>(false, "Unknown error", null, List.of(new ApiResponse.ErrorResponse("UNKNOWN_ERROR", "An error occurred, thats all we know."))));
        } catch (Exception e) {
            String message = e.getMessage().equals("MAXIMUM_REQUEST_REACHED") ? "Otp maximum request reached" : e.getMessage();
            logger.error("2fa Account sign in failed with exception: {}", message);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new ApiResponse<>(false, "Internal error occurred", null, List.of(new ApiResponse.ErrorResponse("INTERNAL_SERVER_ERROR", "Internal error occurred"))));
        }
    }

    @PostMapping(path = "/token/refresh")
    public ResponseEntity<?> tokenRefresh(@RequestBody @Valid TokenRequestBody tokenRequest) {
        try {
            AuthenticationResponse loginResponse = new AuthenticationResponse();

            ResponseEntity<String> response =  authService.generateRefreshToken(tokenRequest.getRefreshToken());

            if (response.getStatusCode() == HttpStatus.OK) {
                String responseBody = response.getBody();
                ObjectMapper objectMapper = new ObjectMapper();
                KeycloakLoginResponse keycloakLoginResponse = objectMapper.readValue(responseBody, KeycloakLoginResponse.class);
                loginResponse.setAccessToken(keycloakLoginResponse.getAccessToken());
                loginResponse.setRefreshToken(keycloakLoginResponse.getRefreshToken());
                loginResponse.setExpiresIn(keycloakLoginResponse.getExpiresIn());
                return ResponseEntity.ok(new ApiResponse<>(true, "Token refreshed successful", loginResponse, null));
            } else {
                logger.error(response.toString());
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                        .body(new ApiResponse<>(false, "Unexpected response", null, List.of(new ApiResponse.ErrorResponse("INTERNAL_SERVER_ERROR", "Unexpected response occurred"))));
            }
        } catch (HttpClientErrorException e) {
            logger.error("refresh failed with exception: {}", e.getMessage());
            if (e.getStatusCode() == HttpStatus.UNAUTHORIZED) {
                return ResponseEntity.status(HttpStatus.OK)
                        .body(new ApiResponse<>(false, "Invalid token", null, List.of(new ApiResponse.ErrorResponse("INVALID_CREDENTIALS", "Invalid broken/ wrong token"))));
            } else if (e.getStatusCode() == HttpStatus.NOT_FOUND) {
                return ResponseEntity.status(HttpStatus.OK)
                        .body(new ApiResponse<>(false, "Invalid token", null, List.of(new ApiResponse.ErrorResponse("NOT_FOUND", "Provided token does not exist or its expired or broken"))));
            } else {
                return ResponseEntity.status(HttpStatus.OK)
                        .body(new ApiResponse<>(false, "Unknown error", null, List.of(new ApiResponse.ErrorResponse("UNKNOWN_ERROR", "An error occurred, that's all we know."))));
            }
        } catch (Exception e) {
            logger.error("Account token refresh failed with exception: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new ApiResponse<>(false, "Internal error occurred", null, List.of(new ApiResponse.ErrorResponse("INTERNAL_SERVER_ERROR", "Internal error occurred"))));
        }
    }

    @PatchMapping(path = "/logout")
    public ResponseEntity<?> logout(HttpServletRequest request, @RequestParam String userId) {
        try {
            authService.keycloakSessionLogout(userId);

            authService.tomcatSessionLogout(request);
            return ResponseEntity.status(HttpStatus.OK)
                    .body(new ApiResponse<>(true, "User session terminated", null, null));

        } catch (Exception e) {
            logger.error("Account logout failed with exception: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new ApiResponse<>(false, "Internal error occurred", null, List.of(new ApiResponse.ErrorResponse("INTERNAL_SERVER_ERROR", "Internal error occurred"))));
        }
    }
    @PutMapping(path = "/sessions/terminate")
    public ResponseEntity<?> terminateSessions(HttpServletRequest request, @RequestParam String userId) {
        try {
            authService.keycloakTerminateAllActiveSessionsLogout(userId);
            authService.tomcatSessionLogout(request);
            return ResponseEntity.status(HttpStatus.OK)
                    .body(new ApiResponse<>(true, "User session terminated", null, null));
        } catch (Exception e) {
            logger.error("Account sessions termination failed with exception: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new ApiResponse<>(false, "Internal error occurred", null, List.of(new ApiResponse.ErrorResponse("INTERNAL_SERVER_ERROR", "Internal error occurred"))));
        }
    }


    @PatchMapping("{userId}/2fa/settings/{status2fa}")
    public ResponseEntity<String> enableTwoFactorAuth(@PathVariable String userId, @PathVariable boolean status2fa) {
        UserSettings updatedSettings = userSettingsService.update2FASettings(userId, status2fa);
        //TODO format response to normalized responses
        return ResponseEntity.ok("Two-Factor Authentication enabled for user: " + userId);
    }







    @PostMapping("/users/{userId}/roles")
    public ResponseEntity<String> assignRole(@PathVariable String userId, @RequestParam List<String> roleName) {
        authService.assignRoleToUser(userId, roleName);
        //TODO format response
        return ResponseEntity.ok("Role assigned to user: " + userId);
    }



    @PostMapping("/admin-authorize")
    public ResponseEntity<?> adminLogin(@Valid @RequestBody AutologinRequest loginRequest) {
        try {
            UserRepresentation userDetails = authService.getUserByUsername(loginRequest.getUsername());
            AuthenticationResponse loginResponse = new AuthenticationResponse();
            if (userDetails == null) {
                return ResponseEntity.status(HttpStatus.OK)
                        .body(new ApiResponse<>(false, "Invalid username or password", null,
                                List.of(new ApiResponse.ErrorResponse("INVALID_CREDENTIALS", "Invalid username or password"))));
            }


            Map<String, List<String>> userRoles = authService.getUserRoles(userDetails.getId());
            List<String> allowedRealmRoles = Arrays.asList(
                    "OFFICER_LEAD", "SYS_ADMIN", "AGENT", "AGENT_LEAD", "OFFICER", "MANAGER"
            );

            boolean hasAllowedRealmRole = userRoles.getOrDefault("roles", Collections.emptyList())
                    .stream()
                    .map(String::toUpperCase)
                    .anyMatch(allowedRealmRoles::contains);

            if (hasAllowedRealmRole) {
                if(!userDetails.isEnabled() || !userDetails.isEmailVerified()){
                    Map<String, Object> response = new HashMap<>();
                    response.put("isDisabled", true);
                    return ResponseEntity.status(HttpStatus.OK)
                            .body(new ApiResponse<>(false, "Account disabled", response, List.of(new ApiResponse.ErrorResponse("ACCOUNT_DISABLED", "This account is disabled to access this system"))));
                }

                ResponseEntity<String> response =  authService.authenticateUser(loginRequest.getUsername(), loginRequest.getPassword());

                if (response.getStatusCode() == HttpStatus.OK) {

                    boolean isEmailVerified = Boolean.TRUE.equals(userDetails.isEmailVerified());
                    loginResponse.setEmailVerified(isEmailVerified);

                    loginResponse.setOTPRequired(true);
                    OtpRequest otpRequest = authService.getOtpRequest(loginRequest, userDetails, OTPTypes.LOGIN_VERIFICATION);
                    otpClient.generateOtp(otpRequest);

                    String responseBody = response.getBody();
                    ObjectMapper objectMapper = new ObjectMapper();
                    KeycloakLoginResponse keycloakLoginResponse = objectMapper.readValue(responseBody, KeycloakLoginResponse.class);
                    loginResponse.setAccessToken(keycloakLoginResponse.getAccessToken());
                    loginResponse.setIdToken(keycloakLoginResponse.getSessionState());
                    loginResponse.setRefreshToken(keycloakLoginResponse.getRefreshToken());
                    loginResponse.setExpiresIn(keycloakLoginResponse.getExpiresIn());
                    loginResponse.setUserId(userDetails.getId());
                    loginResponse.setRoles(userRoles);

                    loginResponse.setFirstName(userDetails.getFirstName());
                    loginResponse.setLastName(userDetails.getLastName());
                    loginResponse.setFirstTimeLogin(false);
                    loginResponse.setRequiresPasswordReset(false);

                    return ResponseEntity.ok(new ApiResponse<>(true, "Log in successful", loginResponse, null));
                } else {
                    return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                            .body(new ApiResponse<>(false, "Unexpected response", null, List.of(new ApiResponse.ErrorResponse("INTERNAL_SERVER_ERROR", "Unexpected response occurred"))));
                }
            } else {
                return ResponseEntity.status(HttpStatus.OK)
                        .body(new ApiResponse<>(false, "Forbidden access", null, List.of(new ApiResponse.ErrorResponse("FORBIDDEN_ACCESS", "You are not allowed to have access to this service"))));
            }
        } catch (HttpClientErrorException e) {
            logger.error("Admin Account sign in failed with keycloak exception: {}", e.getMessage());
            if (e.getStatusCode() == HttpStatus.UNAUTHORIZED) {
                userSettingsService.updateLoginAttempts(loginRequest.getUsername(), true);
                return ResponseEntity.status(HttpStatus.OK)
                        .body(new ApiResponse<>(false, "Invalid username or password", null, List.of(new ApiResponse.ErrorResponse("INVALID_CREDENTIALS", "Invalid username or password"))));
            } else if (e.getStatusCode() == HttpStatus.NOT_FOUND) {
                return ResponseEntity.status(HttpStatus.OK)
                        .body(new ApiResponse<>(false, "Invalid username or password", null, List.of(new ApiResponse.ErrorResponse("NOT_FOUND", "Provided credential do not exist or they are wrong"))));
            } else {
                return ResponseEntity.status(HttpStatus.OK)
                        .body(new ApiResponse<>(false, "Unknown error", null, List.of(new ApiResponse.ErrorResponse("UNKNOWN_ERROR", "An error occurred, thats all we know."))));
            }
        } catch (Exception e) {
            String message = e.getMessage().equals("MAXIMUM_REQUEST_REACHED") ? "Otp maximum request reached" : e.getMessage();
            logger.error("Admin Account sign in failed with exception: {}", message);
            if(e.getMessage().equals("MAXIMUM_REQUEST_REACHED")){
                return ResponseEntity.status(HttpStatus.OK)
                        .body(new ApiResponse<>(false, "Otp maximum request reached", null, null));
            }

            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new ApiResponse<>(false, "Internal error occurred", null, List.of(new ApiResponse.ErrorResponse("INTERNAL_SERVER_ERROR", "Internal error occurred"))));
        }
    }
    @PutMapping("/admin-authorize/mfa")
    public ResponseEntity<?> admin2faApproval(@RequestBody Map<String, Object> loginRequest) {
        try {
            String username = loginRequest.get("username").toString();
            String password = loginRequest.get("password").toString();

            OtpRequest otpRequest = new OtpRequest();
            UserRepresentation userDetails = authService.getUserByUsername(username);;
            if(loginRequest.get("userId") != null){
                otpRequest.setUserId(loginRequest.get("userId").toString());
            }else{
                if(userDetails != null){
                    otpRequest.setUserId(userDetails.getId());
                }
            }

            otpRequest.setOtpType(OTPTypes.LOGIN_VERIFICATION);
            otpRequest.setOtp(Integer.valueOf(loginRequest.get("otpNumber").toString()));
            otpRequest.setCommunicationType(CommunicationType.valueOf(loginRequest.get("communicationType").toString()));
            otpRequest.setContactAddress(username);

            ApiResponse<?> apiResponse = otpClient.verifyOtp(otpRequest);
            if (apiResponse.isStatus()) {
                userSettingsService.updateLoginAttempts(username, false);

                AuthenticationResponse loginResponse = new AuthenticationResponse();

                ResponseEntity<String> response =  authService.authenticateUser(username, password);

                if (response.getStatusCode() == HttpStatus.OK) {

                    loginResponse.setEmailVerified(true);
                    loginResponse.setOTPRequired(false);

                    assert userDetails != null;

                    Map<String, List<String>> userRoles = authService.getUserRoles(userDetails.getId());

                    String responseBody = response.getBody();
                    ObjectMapper objectMapper = new ObjectMapper();
                    KeycloakLoginResponse keycloakLoginResponse = objectMapper.readValue(responseBody, KeycloakLoginResponse.class);
                    loginResponse.setAccessToken(keycloakLoginResponse.getAccessToken());
                    loginResponse.setIdToken(keycloakLoginResponse.getSessionState());
                    loginResponse.setRefreshToken(keycloakLoginResponse.getRefreshToken());
                    loginResponse.setExpiresIn(keycloakLoginResponse.getExpiresIn());
                    loginResponse.setUserId(userDetails.getId());
                    loginResponse.setRoles(userRoles);

                    loginResponse.setFirstName(userDetails.getFirstName());
                    loginResponse.setLastName(userDetails.getLastName());
                    loginResponse.setFirstTimeLogin(false);
                    loginResponse.setRequiresPasswordReset(false);

                    return ResponseEntity.ok(new ApiResponse<>(true, "Log in successful", loginResponse, null));
                } else {
                    return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                            .body(new ApiResponse<>(false, "Unexpected response", null, List.of(new ApiResponse.ErrorResponse("INTERNAL_SERVER_ERROR", "Unexpected response occurred"))));
                }
            } else {
                return ResponseEntity.status(HttpStatus.OK)
                        .body(new ApiResponse<>(false, "Unexpected response", null, apiResponse.getErrors()));
            }
        } catch (HttpClientErrorException e) {
            logger.error("Admin 2fa Account sign in failed with keycloak exception: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.OK)
                    .body(new ApiResponse<>(false, "Unknown error", null, List.of(new ApiResponse.ErrorResponse("UNKNOWN_ERROR", "An error occurred, thats all we know."))));
        } catch (Exception e) {
            String message = e.getMessage().equals("MAXIMUM_REQUEST_REACHED") ? "Otp maximum request reached" : e.getMessage();
            logger.error("Admin 2fa Account sign in failed with exception: {}", message);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new ApiResponse<>(false, "Internal error occurred", null, List.of(new ApiResponse.ErrorResponse("INTERNAL_SERVER_ERROR", "Internal error occurred"))));
        }
    }

}

