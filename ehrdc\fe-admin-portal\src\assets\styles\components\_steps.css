.steps {
  @apply flex justify-center text-center mb-6 ;

  &.steps-vertical {
    @apply flex-col items-start;
  }
}
progress {
  border-radius: 5px; /* Full-rounded corners for outer progress bar */
  height: 1rem; /* Equivalent to h-4 in Tailwind */
}

.step-item {
  @apply flex-col justify-center items-center;

  &.step-item-vertical {
    @apply items-start flex-col;
  }
}

.step-item-wrapper {
  @apply flex-col items-center w-full;
}

.step-item-icon {
  min-width: 2.25rem;
  @apply text-lg rounded-full w-9 h-9 flex items-center justify-center font-medium;

  &.step-item-icon-pending {
    @apply border-2 border-gray-300 dark:border-gray-600;
  }

  &.step-item-icon-current {
    @apply border-2;
  }
}

.step-item-content {
  @apply ltr:ml-3 rtl:mr-3 relative;
}

.step-item-title {
  text-align: center;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 20px; /* 142.857% */
  margin-top: 10px;
  @apply w-28 flex justify-center  dark:text-gray-300;

  &.step-item-title-error {
    @apply text-red-500;
  }
}

.step-item-icon-error {
  @apply border-2 border-red-500 text-red-500;
}

.step-clickable {
  @apply cursor-pointer;
}

.step-connect {
  height: 8px;
  border-radius: 5px;
  &.inactive {
    @apply bg-gray-200 dark:bg-gray-600;
  }
  &.step-connect-vertical {
    min-height: 3.5rem;
    width: 2px;
    @apply ltr:ml-4 rtl:mr-4;
  }
}

