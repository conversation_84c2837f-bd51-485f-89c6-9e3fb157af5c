package bw.org.hrdf.account.models.company;

import bw.org.hrdf.account.entity.company.CompanyEntity;
import bw.org.hrdf.account.entity.company.Verification;
import bw.org.hrdf.account.entity.enums.ActionsEnum;
import bw.org.hrdf.account.entity.enums.VerificationTypeEnum;

public class VerificationMapper {

    public static Verification toEntity(VerificationModel model, CompanyEntity company) {
        return new Verification(
                model.getReference(),
                model.getType(),
                model.getStatus(),
                model.getVerifiedAt(),
                model.getExpiryDate(),
                company
        );
    }
    public static VerificationModel toModel(Verification entity) {
        VerificationModel model = new VerificationModel();
        model.setReference(entity.getReference());
        model.setType(entity.getType());
        model.setStatus(entity.getStatus());
        model.setExpiryDate(entity.getExpiryDate());
        model.setVerifiedAt(entity.getVerifiedAt());
        model.setCompanyId(entity.getCompany().getUuid());
        return model;
    }

    public static VerificationModel toActionEntity(Verification entity) {
        VerificationModel model = new VerificationModel();
        model.setReference(entity.getReference());
        model.setType(entity.getType());
        model.setStatus(entity.getStatus());
        model.setExpiryDate(entity.getExpiryDate());
        model.setVerifiedAt(entity.getVerifiedAt());
        model.setCompanyId(entity.getCompany().getUuid());
        return model;
    }

    public static ActionsEnum mapVerificationTypeToActionType(VerificationTypeEnum type){
        if(type.equals(VerificationTypeEnum.COMPANY_NUMBER)){
            return ActionsEnum.VERIFY_CIPA_NUMBER;
        } else if (type.equals(VerificationTypeEnum.BQA_CREDENTIALS)) {
            return ActionsEnum.VERIFY_BQA_DETAILS;
        } else if (type.equals(VerificationTypeEnum.BURS_TIN_NUMBER)) {
            return ActionsEnum.VERIFY_BURS_TIN;
        } else if (type.equals(VerificationTypeEnum.NATIONAL_IDENTIFICATION)) {
            return ActionsEnum.VERIFY_IDENTIFICATION_NUMBER;
        }
        return ActionsEnum.CHANGE_APPLICATION_DETAILS;
    }

    public static String mapVerificationTypeToActionMessage(VerificationTypeEnum type){
        if(type.equals(VerificationTypeEnum.COMPANY_NUMBER)){
            return "Company CIPA Number verification pending";
        } else if (type.equals(VerificationTypeEnum.BQA_CREDENTIALS)) {
            return "BQA certification verification pending";
        } else if (type.equals(VerificationTypeEnum.BURS_TIN_NUMBER)) {
            return "BURS TIN Number verification is pending";
        } else if (type.equals(VerificationTypeEnum.NATIONAL_IDENTIFICATION)) {
            return "ID Omang verification is pending";
        }
        return "Application changes requested";
    }
}

