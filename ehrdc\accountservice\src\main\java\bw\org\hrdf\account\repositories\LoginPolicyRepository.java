package bw.org.hrdf.account.repositories;

import bw.org.hrdf.account.entity.UserLoginPolicies;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface LoginPolicyRepository extends JpaRepository<UserLoginPolicies, Integer> {

    @Query("SELECT o FROM UserLoginPolicies o WHERE o.username = :username")
    Optional<UserLoginPolicies> findByUser(String username);
}
