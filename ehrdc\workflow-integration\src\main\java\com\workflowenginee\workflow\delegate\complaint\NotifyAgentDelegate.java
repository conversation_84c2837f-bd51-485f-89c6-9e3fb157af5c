package com.workflowenginee.workflow.delegate.complaint;

import java.util.Map;

import org.flowable.engine.delegate.DelegateExecution;
import org.flowable.engine.delegate.JavaDelegate;
import org.springframework.stereotype.Component;

@Component("notifyAgentDelegate")
public class NotifyAgentDelegate implements JavaDelegate {

    @Override
    public void execute(DelegateExecution execution) {
        String processInstanceId = execution.getProcessInstanceId();
        String complaintId = (String) execution.getVariable("complaintId");
        String role = (String) execution.getVariable("role");

        System.out.println("[Process: " + processInstanceId + "] Notifying agent for complaint: " + complaintId);

        try {
            Map<String, Object> complaintData = (Map<String, Object>) execution.getVariable("complaintData");
            
            if (complaintData != null) {
                // Log notification action
                System.out.println("[Process: " + processInstanceId + "] Notifying agent about new complaint assignment");
                
                // TODO: Implement actual notification logic here
                // For now, just log the action
                System.out.println("[Process: " + processInstanceId + "] Agent notification sent successfully");
                
                // Set variable to indicate notification was sent
                execution.setVariable("agentNotified", true);

            } else {
                System.err.println("[Process: " + processInstanceId + "] No complaint data available for notification");
                execution.setVariable("agentNotified", false);
            }

        } catch (Exception e) {
            System.err.println("[Process: " + processInstanceId + "] Error sending agent notification: " + e.getMessage());
            e.printStackTrace();
            execution.setVariable("agentNotified", false);
        }
    }
}
