package com.workflowenginee.workflow.delegate.complaint;

import java.util.Map;

import org.flowable.engine.delegate.DelegateExecution;
import org.flowable.engine.delegate.JavaDelegate;
import org.springframework.stereotype.Component;

import com.workflowenginee.workflow.dto.NotifyUsersByRoleDto;
import com.workflowenginee.workflow.service.NotificationComplaintService;
import com.workflowenginee.workflow.util.Enums;

@Component("notifyAgentDelegate")
public class NotifyAgentDelegate implements JavaDelegate {

    private final NotificationComplaintService notificationComplaintService;

    public NotifyAgentDelegate(NotificationComplaintService notificationComplaintService) {
        this.notificationComplaintService = notificationComplaintService;
    }

    @Override
    public void execute(DelegateExecution execution) {
        String processInstanceId = execution.getProcessInstanceId();
        String complaintId = (String) execution.getVariable("complaintId");
        String role = (String) execution.getVariable("role");

        System.out.println("[Process: " + processInstanceId + "] Notifying agent for complaint: " + complaintId);

        try {
            Map<String, Object> complaintData = (Map<String, Object>) execution.getVariable("complaintData");
            
            if (complaintData != null) {
                // Create notification DTO
                NotifyUsersByRoleDto notificationDto = NotifyUsersByRoleDto.builder()
                    .applicationType(Enums.ApplicationType.COMPLAINTS.name())
                    .applicationId(complaintId)
                    .role(Enums.Role.AGENT.name())
                    .notificationType(Enums.NotificationType.NEW_COMPLAINT.name())
                    .message("New complaint has been assigned to you for review")
                    .build();

                // Send notification
                notificationComplaintService.notifyUsersByRole(notificationDto);

                System.out.println("[Process: " + processInstanceId + "] Agent notification sent successfully");
                
                // Set variable to indicate notification was sent
                execution.setVariable("agentNotified", true);

            } else {
                System.err.println("[Process: " + processInstanceId + "] No complaint data available for notification");
                execution.setVariable("agentNotified", false);
            }

        } catch (Exception e) {
            System.err.println("[Process: " + processInstanceId + "] Error sending agent notification: " + e.getMessage());
            e.printStackTrace();
            execution.setVariable("agentNotified", false);
        }
    }
}
