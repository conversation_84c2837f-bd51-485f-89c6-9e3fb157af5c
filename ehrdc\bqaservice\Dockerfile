FROM maven:latest AS build

LABEL maintainer="<PERSON> <<EMAIL>>"

WORKDIR /app

COPY pom.xml ./
COPY src ./src

RUN mvn clean package -DskipTests

FROM openjdk:17-slim

WORKDIR /app
# Set environment variables
ENV SERVER_PORT=3600 \
    SPRING_PROFILES_ACTIVE=local

COPY --from=build /app/target/service-1.0.0.jar ./app.jar

EXPOSE 3600

ENTRYPOINT ["java", "-Dspring.profiles.active=${SPRING_PROFILES_ACTIVE}", "-jar", "./app.jar"]