package bw.org.hrdf.account.service;

import bw.org.hrdf.account.entity.UserSettings;
import bw.org.hrdf.account.entity.enums.PrefsMethod;
import bw.org.hrdf.account.repositories.UserSettingsRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

@Service
public class UserSettingsService {

    @Autowired
    private UserSettingsRepository userSettingsRepository;

    public UserSettings getUserSettings(String userId) {
        return userSettingsRepository.findByUserId(userId)
                .orElseThrow(() -> new RuntimeException("Settings not found for user ID: " + userId));
    }

    @Transactional
    public UserSettings update2FASettings(String userId, boolean isEnabled) {
        UserSettings settings = userSettingsRepository.findByUserId(userId)
                .orElse(new UserSettings()); // Create new if not exists

        settings.setUserId(userId);
        settings.set2FAEnabled(isEnabled);
        settings.setLastUpdated(LocalDateTime.now());

        return userSettingsRepository.save(settings);
    }

    @Transactional
    public UserSettings updateUserSettings(String userId, PrefsMethod method, String phoneNumber, String backupEmail) {
        UserSettings settings = userSettingsRepository.findByUserId(userId)
                .orElse(new UserSettings()); // Create new if not exists

        settings.setUserId(userId);
        settings.setPreferred2FAMethod(method);
        settings.setPhoneNumber(phoneNumber);
        settings.setBackupEmail(backupEmail);
        settings.setLastUpdated(LocalDateTime.now());

        return userSettingsRepository.save(settings);
    }
}
