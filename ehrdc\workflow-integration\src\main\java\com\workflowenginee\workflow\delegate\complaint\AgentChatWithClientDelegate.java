package com.workflowenginee.workflow.delegate.complaint;

import java.util.Map;

import org.flowable.engine.delegate.DelegateExecution;
import org.flowable.engine.delegate.JavaDelegate;
import org.springframework.stereotype.Component;

import com.workflowenginee.workflow.dto.NotifyToClientDto;
import com.workflowenginee.workflow.service.NotificationComplaintService;
import com.workflowenginee.workflow.util.Enums;

@Component("agentChatWithClientDelegate")
public class AgentChatWithClientDelegate implements JavaDelegate {

    private final NotificationComplaintService notificationComplaintService;

    public AgentChatWithClientDelegate(NotificationComplaintService notificationComplaintService) {
        this.notificationComplaintService = notificationComplaintService;
    }

    @Override
    public void execute(DelegateExecution execution) {
        String processInstanceId = execution.getProcessInstanceId();
        String complaintId = (String) execution.getVariable("complaintId");
        String role = (String) execution.getVariable("role");

        System.out.println("[Process: " + processInstanceId + "] Agent initiating chat with client for complaint: " + complaintId);

        try {
            Map<String, Object> complaintData = (Map<String, Object>) execution.getVariable("complaintData");
            
            if (complaintData != null) {
                // Log the chat initiation
                System.out.println("[Process: " + processInstanceId + "] Agent is starting chat session with client");
                
                // Set chat session variables
                execution.setVariable("chatSessionActive", true);
                execution.setVariable("chatInitiatedBy", "AGENT");
                execution.setVariable("chatReason", "COMPLAINT_DISCUSSION");
                execution.setVariable("chatStartTime", System.currentTimeMillis());

                // Notify client about chat session
                try {
                    NotifyToClientDto clientNotification = NotifyToClientDto.builder()
                        .applicationType(Enums.ApplicationType.COMPLAINTS.name())
                        .applicationId(complaintId)
                        .notificationType(Enums.NotificationType.CHAT_STARTED.name())
                        .message("Our agent has started a chat session to discuss your complaint")
                        .build();

                    notificationComplaintService.notifyToClient(clientNotification);
                    
                    System.out.println("[Process: " + processInstanceId + "] Client notified about chat session");
                    
                } catch (Exception notificationEx) {
                    System.err.println("[Process: " + processInstanceId + "] Failed to send chat notification: " + notificationEx.getMessage());
                }

                // After chat completion, mark complaint as resolved
                execution.setVariable("complaintStatus", "RESOLVED_VIA_CHAT");
                execution.setVariable("resolvedBy", "AGENT");
                execution.setVariable("resolvedAt", System.currentTimeMillis());

                System.out.println("[Process: " + processInstanceId + "] Agent chat session completed successfully");

            } else {
                System.err.println("[Process: " + processInstanceId + "] No complaint data available for chat");
                execution.setVariable("chatSessionActive", false);
            }

        } catch (Exception e) {
            System.err.println("[Process: " + processInstanceId + "] Error in agent chat: " + e.getMessage());
            e.printStackTrace();
            execution.setVariable("chatSessionActive", false);
        }
    }
}
