package com.workflowenginee.workflow.delegate.complaint;

import java.util.Map;

import org.flowable.engine.delegate.DelegateExecution;
import org.flowable.engine.delegate.JavaDelegate;
import org.springframework.stereotype.Component;

@Component("agentChatWithClientDelegate")
public class AgentChatWithClientDelegate implements JavaDelegate {

    @Override
    public void execute(DelegateExecution execution) {
        String processInstanceId = execution.getProcessInstanceId();
        String complaintId = (String) execution.getVariable("complaintId");
        String role = (String) execution.getVariable("role");

        System.out.println("[Process: " + processInstanceId + "] Agent initiating chat with client for complaint: " + complaintId);

        try {
            Map<String, Object> complaintData = (Map<String, Object>) execution.getVariable("complaintData");
            
            if (complaintData != null) {
                // Log the chat initiation
                System.out.println("[Process: " + processInstanceId + "] Agent is starting chat session with client");
                
                // Set chat session variables
                execution.setVariable("chatSessionActive", true);
                execution.setVariable("chatInitiatedBy", "AGENT");
                execution.setVariable("chatReason", "COMPLAINT_DISCUSSION");
                execution.setVariable("chatStartTime", System.currentTimeMillis());

                // TODO: Notify client about chat session
                System.out.println("[Process: " + processInstanceId + "] Client would be notified about chat session");

                // After chat completion, mark complaint as resolved
                execution.setVariable("complaintStatus", "RESOLVED_VIA_CHAT");
                execution.setVariable("resolvedBy", "AGENT");
                execution.setVariable("resolvedAt", System.currentTimeMillis());

                System.out.println("[Process: " + processInstanceId + "] Agent chat session completed successfully");

            } else {
                System.err.println("[Process: " + processInstanceId + "] No complaint data available for chat");
                execution.setVariable("chatSessionActive", false);
            }

        } catch (Exception e) {
            System.err.println("[Process: " + processInstanceId + "] Error in agent chat: " + e.getMessage());
            e.printStackTrace();
            execution.setVariable("chatSessionActive", false);
        }
    }
}
