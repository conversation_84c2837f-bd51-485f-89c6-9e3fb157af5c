package com.workflowenginee.workflow.api;

import com.workflowenginee.workflow.util.ApiResponse;
import org.springframework.stereotype.Component;
import java.util.Map;
import java.util.UUID;

@Component
public class WorkplaceLearningClientFallback implements WorkplaceLearningClient {

    private ApiResponse<?> createFallbackResponse() {
        ApiResponse<Object> response = new ApiResponse<>();
        response.setStatus(false);
        response.setMessage("Workplace Learning service is currently unavailable");
        response.setData(null);
        response.setErrors(null);
        return response;
    }

    @Override
    public ApiResponse<?> getApplicationByReferenceNumber(String referenceNumber) {
        return createFallbackResponse();
    }

    @Override
    public ApiResponse<?> getApplicationById(String id) {
        return createFallbackResponse();
    }

    @Override
    public ApiResponse<?> getApplicationById(UUID id) {
        return createFallbackResponse();
    }

    @Override
    public ApiResponse<?> searchByReferenceNumber(String applicationNumber) {
        return createFallbackResponse();
    }

    @Override
    public ApiResponse<?> getComplaintById(String id) {
        return createFallbackResponse();
    }

    @Override
    public ApiResponse<?> updateComplaintStatus(String complaintId, Map<String, Object> statusUpdate) {
        return createFallbackResponse();
    }

    @Override
    public ApiResponse<?> assignComplaint(String complaintId, Map<String, Object> assignmentData) {
        return createFallbackResponse();
    }

    @Override
    public ApiResponse<?> escalateComplaint(String complaintId, Map<String, Object> escalationData) {
        return createFallbackResponse();
    }

    @Override
    public ApiResponse<?> closeComplaint(String complaintId, Map<String, Object> closureData) {
        return createFallbackResponse();
    }

    @Override
    public ApiResponse<?> initiateChatSession(String complaintId, Map<String, Object> chatData) {
        return createFallbackResponse();
    }
}