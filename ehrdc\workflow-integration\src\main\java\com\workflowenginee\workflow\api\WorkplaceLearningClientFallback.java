package com.workflowenginee.workflow.api;

import com.workflowenginee.workflow.util.ApiResponse;
import org.springframework.stereotype.Component;
import java.util.Map;
import java.util.UUID;

@Component
public class WorkplaceLearningClientFallback implements WorkplaceLearningClient {
    
    @Override
    public ApiResponse<?> getApplicationByReferenceNumber(String referenceNumber) {
        return new ApiResponse<>(
            false,
            "Workplace Learning service is currently unavailable",
            null,
            null
        );
    }

    @Override
    public ApiResponse<?> getApplicationById(String id) {
        return new ApiResponse<>(
            false,
            "Workplace Learning service is currently unavailable",
            null,
            null
        );
    }

    @Override
    public ApiResponse<?> getApplicationById(UUID id) {
        return new ApiResponse<>(
            false,
            "Workplace Learning service is currently unavailable",
            null,
            null
        );
    }

    @Override
    public ApiResponse<?> searchByReferenceNumber(String applicationNumber) {
        return new ApiResponse<>(
            false,
            "Workplace Learning service is currently unavailable",
            null,
            null
        );
    }

    @Override
    public ApiResponse<?> getComplaintById(String id) {
        return new ApiResponse<>(
            false,
            "Workplace Learning service is currently unavailable",
            null,
            null
        );
    }

    @Override
    public ApiResponse<?> updateComplaintStatus(String id, Map<String, Object> statusUpdate) {
        return new ApiResponse<>(
            false,
            "Workplace Learning service is currently unavailable",
            null,
            null
        );
    }

    @Override
    public ApiResponse<?> addComplaintComment(String id, Map<String, Object> comment) {
        return new ApiResponse<>(
            false,
            "Workplace Learning service is currently unavailable",
            null,
            null
        );
    }
}