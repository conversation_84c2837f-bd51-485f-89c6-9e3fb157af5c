import toast from '@/components/ui/toast'
import Notification from '@/components/ui/Notification'
import ConfirmDialog from '@/components/shared/ConfirmDialog'
import { useDisclosure } from '@reactuses/core'
import { useEffect, useState } from 'react'
import { Field, FieldProps, Form, Formik } from 'formik'
import { FormContainer, FormItem } from '@/components/ui'
import { RichTextEditor } from '@/components/shared'

const AppealDialog = ({ id, openDialog, setOpenDialog }) => {
    const { isOpen, onOpen, onClose } = useDisclosure()
    const [success, setSuccess] = useState(false)
    useEffect(() => {
        if (openDialog) {
            onOpen()
        } else {
            onClose()
        }
    }, [openDialog])


    const onCloseDialog = async () => {
        setOpenDialog(false)
    }

    //confirm delete
    const onDelete = async () => {
        try {
            const success = true
            if (success) {
                setSuccess(true)
                toast.push(
                    <Notification
                        title={'Saved Successfully '}
                        type="success"
                        duration={2500}
                    >
                        Record saved successfuly
                    </Notification>,
                    {
                        placement: 'top-end'
                    }
                )
            }
        } catch (e) {
            toast.push(
                <Notification
                    title={'Failed to save'}
                    type="danger"
                    duration={2500}
                >
                    Record failed to save
                </Notification>,
                {
                    placement: 'top-end'
                }
            )
        } finally {
            onCloseDialog()
        }


    }

    return (
        <ConfirmDialog
            isOpen={isOpen}
            type="warning"
            title="Log Complaint"
            confirmButtonColor="red-600"
            onClose={onCloseDialog}
            onRequestClose={onCloseDialog}
            onCancel={onCloseDialog}
            onConfirm={onDelete}
        >
            <p>
                Please provide your complaint description. Provide specific details about the issue or decision..
            </p>

            <Formik
                //innerRef={formikRef}
                initialValues={{
                    message: '',
                }}
                //validationSchema={validationSchema}
                onSubmit={() => {
                    //onSend()
                }}
            >
                {({ touched, errors }) => (
                    <Form>
                        <FormContainer>


                            <FormItem
                                label={"Message"}
                                className="mb-0"
                                labelClass="!justify-start"
                                invalid={errors.message && touched.message}
                                errorMessage={errors.message}
                            >
                                <Field name="message">
                                    {({ field, form }: FieldProps) => (
                                        <RichTextEditor
                                            //ref={editorRef}
                                            value={field.value}
                                            onChange={(val) =>
                                                form.setFieldValue(field.name, val)
                                            }
                                        />
                                    )}
                                </Field>
                            </FormItem>
                        </FormContainer>
                    </Form>
                )}


            </Formik>
        </ConfirmDialog>
    )
}

export default AppealDialog
