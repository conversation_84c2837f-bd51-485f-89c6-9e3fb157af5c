package bw.org.hrdc.weblogic.apigateway;

import java.time.Duration;
import java.time.LocalDateTime;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.circuitbreaker.resilience4j.ReactiveResilience4JCircuitBreakerFactory;
import org.springframework.cloud.circuitbreaker.resilience4j.Resilience4JConfigBuilder;
import org.springframework.cloud.client.circuitbreaker.Customizer;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.client.loadbalancer.LoadBalanced;
import org.springframework.cloud.gateway.filter.ratelimit.KeyResolver;
import org.springframework.cloud.gateway.filter.ratelimit.RedisRateLimiter;
import org.springframework.cloud.gateway.route.RouteLocator;
import org.springframework.cloud.gateway.route.builder.RouteLocatorBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.http.MediaType;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.server.RequestPredicates;
import org.springframework.web.reactive.function.server.RouterFunction;
import org.springframework.web.reactive.function.server.RouterFunctions;
import org.springframework.web.reactive.function.server.ServerResponse;

import bw.org.hrdc.weblogic.apigateway.util.FallbackUtil;
import io.github.resilience4j.circuitbreaker.CircuitBreakerConfig;
import io.github.resilience4j.timelimiter.TimeLimiterConfig;
import reactor.core.publisher.Mono;

@EnableDiscoveryClient
@SpringBootApplication
public class ApiGatewayApplication {

	public static void main(String[] args) {
		SpringApplication.run(ApiGatewayApplication.class, args);
	}

	@Bean
	public RouteLocator routeConfig(RouteLocatorBuilder routeLocatorBuilder) {
		return routeLocatorBuilder.routes()
				.route("WORKPLACE-LEARNING", p -> p
						.path("/api/v1/workplace-learning/**", "/api/v1/ncbsc/**", "/api/v1/complaints/**", "/api/v1/pre-approval-applications/**")
						.filters(f -> f.circuitBreaker(config -> config.setName("workplaceCircuitBreaker")
								.setFallbackUri("forward:/contactSupport"))
								.addResponseHeader("X-Response-Time", LocalDateTime.now().toString()))
						.uri("lb://WORKPLACE-LEARNING"))

				.route(p -> p
						.path("/api/v1/communication/**")
						.filters( f -> f.rewritePath("/api/v1/communication/(?<segment>.*)","/${segment}")
								.addResponseHeader("X-Response-Time", LocalDateTime.now().toString())
								.circuitBreaker(config -> config.setName("communicationCircuitBreaker")
										.setFallbackUri("forward:/contactSupport")))
						.uri("lb://COMMUNICATION"))
				.route("AUTH-SERVICE", p -> p
						.path("/api/v1/oauth2/**")
						.filters(f -> f.circuitBreaker(config -> config.setName("authServiceCircuitBreaker")
								.setFallbackUri("forward:/fallbackAuth")))
						.uri("lb://AUTH-SERVICE"))
				.route("OTP-SERVICE", p -> p
						.path("/api/v1/otp/**")
						.filters(f -> f.circuitBreaker(config -> config.setName("otpServiceCircuitBreaker")
								.setFallbackUri("forward:/fallbackOtp")))
						.uri("lb://OTP-SERVICE"))

				.route("ACCOUNT-SERVICE", p -> p
						.path("/api/v1/registration/**", "/api/v1/user/**", "/api/v1/company/**")
						.filters(f -> f.circuitBreaker(config -> config.setName("accountServiceCircuitBreaker")
								.setFallbackUri("forward:/fallbackAccount")))
						.uri("lb://ACCOUNT-SERVICE"))

				.route("INTEGRATION-SERVICE", p -> p
						.path("/api/v1/integration/**")
						.filters(f -> f.circuitBreaker(config -> config.setName("integrationServiceCircuitBreaker")
								.setFallbackUri("forward:/fallbackIntegration")))
						.uri("lb://INTEGRATION-SERVICE"))
				.route(p -> p
						.path("/api/v1/dms/**")
						.filters( f -> f.rewritePath("/api/v1/dms/(?<segment>.*)","/${segment}")
								.addResponseHeader("X-Response-Time", LocalDateTime.now().toString())
								.circuitBreaker(config -> config.setName("dmsCircuitBreaker")
										.setFallbackUri("forward:/contactSupport")))
						.uri("lb://DMS"))

				.build();
	}

	@Bean
	public RedisRateLimiter redisRateLimiter() {
		return new RedisRateLimiter(1, 1, 1);
	}

	@Bean
	KeyResolver userKeyResolver() {
		return exchange -> Mono.justOrEmpty(exchange.getRequest().getHeaders().getFirst("user"))
				.defaultIfEmpty("anonymous");
	}
	@Bean
	@LoadBalanced
	RestTemplate loadBalancedRestTemplate() {
		return new RestTemplate();
	}

	@Bean
	@LoadBalanced
	public WebClient.Builder loadBalancedWebClientBuilder() {
		return WebClient.builder();
	}

	@Value("classpath:/static/index.html")
	private Resource indexHtml;

	/**
	 * workaround solution for forwarding to index.html
	 * @see <a href="https://github.com/spring-projects/spring-boot/issues/9785">#9785</a>
	 */
	@Bean
	RouterFunction<?> routerFunction() {
        return RouterFunctions.resources("/**", new ClassPathResource("static/"))
                .andRoute(RequestPredicates.GET("/"),
                        request -> ServerResponse.ok().contentType(MediaType.TEXT_HTML).bodyValue(indexHtml));
	}

	/**
	 * Default Resilience4j circuit breaker configuration
	 */
	@Bean
	public Customizer<ReactiveResilience4JCircuitBreakerFactory> defaultCustomizer() {
		return factory -> factory.configureDefault(id -> new Resilience4JConfigBuilder(id)
				.circuitBreakerConfig(CircuitBreakerConfig.ofDefaults())
				.timeLimiterConfig(TimeLimiterConfig.custom().timeoutDuration(Duration.ofSeconds(4)).build())
				.build());
	}

	@Bean
	public RouterFunction<ServerResponse> fallbackHandlers() {
		return RouterFunctions.route(RequestPredicates.path("/fallbackAuth"),
						request -> FallbackUtil.generateFallbackResponse("auth-service", "Auth service is temporarily unavailable.", "503"))
				.andRoute(RequestPredicates.path("/fallbackOtp"),
						request -> FallbackUtil.generateFallbackResponse("otp-service", "OTP service is temporarily unavailable.", "503"))
				.andRoute(RequestPredicates.path("/fallbackAccount"),
						request -> FallbackUtil.generateFallbackResponse("account-service", "Account service is temporarily unavailable.", "503"));
	}
}
