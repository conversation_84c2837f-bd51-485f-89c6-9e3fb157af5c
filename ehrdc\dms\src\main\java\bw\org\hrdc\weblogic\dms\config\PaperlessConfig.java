package bw.org.hrdc.weblogic.dms.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.reactive.function.client.WebClient;

@Configuration
public class PaperlessConfig {
    @Value("${paperless.api.token}")
    private String apiToken;

    @Bean
    public WebClient paperlessWebClient(WebClient.Builder builder, @Value("${paperless.api.url}") String baseUrl) {
        return builder
                .baseUrl(baseUrl)
                .defaultHeader("Authorization", "Token " + apiToken)
                .build();
    }
}