package bw.org.hrdf.account.service.company;

import bw.org.hrdf.account.entity.company.Actions;
import bw.org.hrdf.account.entity.company.ApplicationEntity;
import bw.org.hrdf.account.entity.enums.StateEnum;
import bw.org.hrdf.account.entity.enums.StatusEnum;
import bw.org.hrdf.account.entity.enums.VerificationStatusEnum;
import bw.org.hrdf.account.models.company.CompanyRequest;
import bw.org.hrdf.account.models.company.VerificationModel;
import bw.org.hrdf.account.repositories.company.ActionsRepository;
import bw.org.hrdf.account.repositories.company.ApplicationRepository;
import org.jsoup.Jsoup;
import org.jsoup.safety.Whitelist;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

@Service
public class ApplicationService {

    @Autowired
    private ApplicationRepository applicationRepository;

    @Autowired
    private ActionsRepository actionsRepository;

    private StateEnum getApplicationStatus(CompanyRequest companyRequest, List<VerificationModel> verifications) {
        if (verifications.stream().anyMatch(v -> !VerificationStatusEnum.VERIFIED.equals(v.getStatus()))) {
            return StateEnum.DRAFT;
        }
        return companyRequest.getRegistrationApplication().getState();
    }

    public ApplicationEntity saveApplication(ApplicationEntity application) {
        return applicationRepository.save(application);
    }

    public Actions createAction(Actions actionObject) {
        return actionsRepository.save(actionObject);
    }

    @Transactional()
    public List<Actions> createActions(List<Actions> actionObjects) {
        return actionsRepository.saveAllAndFlush(actionObjects);
    }

    public Optional<ApplicationEntity> getApplicationByReferenceNumber(String referenceNumber) {
        return applicationRepository.findByReferenceNumber(referenceNumber);
    }

    public Optional<ApplicationEntity> getApplicationByUuid(String id) {
        return applicationRepository.findByUuid(id);
    }

    public Optional<Actions> getAction(String id) {
        return actionsRepository.findByUuid(id);
    }

    @Transactional
    public int updateApplicationEntity(String id, String role, String userId) {
        return applicationRepository.updateApplicationAssignedUser(id, role, userId );
    }
    @Transactional
    public int updateApplicationStatus(String reference, StateEnum state, StatusEnum status) {
        return applicationRepository.updateApplicationStatus(reference, state, status );
    }
//
//    public List<ApplicationEntity> findByStatus(String status) {
//        return ((ApplicationRepository) repository).findByStatus(status);
//    }
//
//    public Optional<ApplicationEntity> findByOrganisationReference(String organisationReference) {
//        return ((ApplicationRepository) repository).findByOrganisationId(organisationReference);
//    }
//
//    public List<Actions> getActionsForApplication(Long applicationId) {
//        return actionsRepository.findByRegistrationApplicationId(applicationId);
//    }

    @Transactional()
    public int updateApplication(ApplicationEntity application) {

        return applicationRepository.updateApplication(
                application.getUuid(),
                application.getStatus(),
                application.getState(),
                application.getApplicationSubmissionDate(),
                application.getAgentLead(),
                application.getAgent(),
                application.getManager()
        );
    }

    public String sanitizeHtml(String html) {
        return Jsoup.clean(html, Whitelist.relaxed());
    }
}
