package bw.org.hrdc.weblogic.dms.controller;

import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.reactive.function.BodyInserters;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

@RestController
@RequestMapping("/api/documents")
public class DocumentController {
    private final WebClient webClient;

    public DocumentController(WebClient webClient) {
        this.webClient = webClient;
    }

    @PostMapping(consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public Mono<ResponseEntity<String>> uploadDocument(@RequestParam("file") MultipartFile file) {
        return webClient.post()
                .uri("/api/documents/post_document/")
                .contentType(MediaType.MULTIPART_FORM_DATA)
                .body(BodyInserters.fromMultipartData("document", file.getResource()))
                .retrieve()
                .toEntity(String.class);
    }

    @GetMapping
    public Mono<ResponseEntity<String>> getDocuments() {
        return webClient.get()
                .uri("/api/documents/")
                .retrieve()
                .toEntity(String.class);
    }

    @GetMapping("/{id}/download")
    public ResponseEntity<byte[]> downloadDocument(@PathVariable Integer id) {
        byte[] fileBytes = webClient.get()
                .uri("/api/documents/{id}/download/", id)
                .retrieve()
                .bodyToMono(byte[].class)
                .block();

        return ResponseEntity.ok()
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"document.pdf\"")
                .body(fileBytes);
    }
}


