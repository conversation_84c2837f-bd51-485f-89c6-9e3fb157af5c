
import { type ReactNode } from 'react'
import type { CommonProps } from '@/@types/common'
import { useLocation } from 'react-router-dom';
import MobileNav from '@/components/template/MobileNav'
import SideNavToggle from '@/components/template/SideNavToggle'
import SidePanel from '@/components/template/SidePanel'
import Notification from '@/components/template/Notification'
import UserDropdown from '@/components/template/UserDropdown'
import Search from '@/components/template/Search'
import classNames from 'classnames'
import { HEADER_HEIGHT_CLASS } from '@/constants/theme.constant'
import useResponsive from '@/utils/hooks/useResponsive'
import { startCase } from 'lodash';

interface HeaderProps extends CommonProps {
    headerStart?: ReactNode
    headerEnd?: ReactNode
    headerMiddle?: ReactNode
    container?: boolean
}

const Header = (props: HeaderProps) => {
    const { className, container } = props;

    const location = useLocation();
    let firstPathSegment: string = location.pathname.split('/')[1] || 'Home';
    firstPathSegment = decodeURIComponent(firstPathSegment).replace('-', ' ');

    const renderTitle = () =>{
        if(firstPathSegment === "ncbsc") return "Non-Credit Bearing Short Course"
        if(firstPathSegment === "ccms") return "Contact Center Management"
        if(firstPathSegment === "funds") return "Fund Reimbursement"
        if(firstPathSegment === "workplace") return "Workplace Learning"
        if(firstPathSegment === "home") return "Dashboard"
        
        return firstPathSegment;
    }

    const HeaderStart = () => {
        const { smaller } = useResponsive()
        return (
            <>
                {smaller.lg ? <MobileNav /> : <SideNavToggle />}
            </>
        )
    }
    const HeaderMiddle = () => {
        return (
            <div className="text-dark font-bold text-2xl ">
                {startCase(renderTitle())}
            </div>
        )
    }
    const HeaderEnd = () => {
        return (
            <>
                <div className="font-bold text-xl  flex items-center  lg:gap-4 lg:px-8">
                    <Search />
                    <UserDropdown hoverable={true} />
                    <SidePanel />
                    <Notification />
                </div>
            </>
        )
    }
    return (
        <header className={classNames('header', className)}>
            <div
                className={classNames(
                    'header-wrapper',
                    HEADER_HEIGHT_CLASS,
                    container && 'container mx-auto'
                )}
            >
                <div className="grid grid-cols-12  w-full h-full">
                    <div className={'flex col-span-3  lg:col-span-1 lg:gap-12 header-action '}>
                        <HeaderStart />
                    </div>

                    <div className={'hidden lg:flex lg:col-span-6  header-action'}>
                        <HeaderMiddle />
                    </div>
                    <div className={'col-span-9 lg:col-span-5 header-action justify-end  '}>
                        <HeaderEnd />
                    </div>
                </div>
            </div>
        </header>
    )
}
export default Header
