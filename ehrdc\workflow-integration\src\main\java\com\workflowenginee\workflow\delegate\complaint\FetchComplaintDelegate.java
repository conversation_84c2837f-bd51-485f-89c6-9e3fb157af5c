package com.workflowenginee.workflow.delegate.complaint;

import java.util.HashMap;
import java.util.Map;

import org.flowable.engine.delegate.DelegateExecution;
import org.flowable.engine.delegate.JavaDelegate;
import org.springframework.stereotype.Component;

import com.workflowenginee.workflow.api.WorkplaceLearningClient;
import com.workflowenginee.workflow.util.ApiResponse;

@Component("fetchComplaintDelegate")
public class FetchComplaintDelegate implements JavaDelegate {

    private final WorkplaceLearningClient workplaceLearningClient;

    public FetchComplaintDelegate(WorkplaceLearningClient workplaceLearningClient) {
        this.workplaceLearningClient = workplaceLearningClient;
    }

    @Override
    public void execute(DelegateExecution execution) {
        String processInstanceId = execution.getProcessInstanceId();
        String complaintId = (String) execution.getVariable("complaintId");
        String role = (String) execution.getVariable("role");

        System.out.println("[Process: " + processInstanceId + "] Fetching complaint details for ID: " + complaintId);

        try {
            ApiResponse<?> response = workplaceLearningClient.getComplaintById(complaintId);

            if (response != null && response.getData() instanceof Map) {
                Map<String, Object> complaintData = (Map<String, Object>) response.getData();
                execution.setVariable("complaintData", complaintData);
                execution.setVariable("complaintId", complaintId);
                execution.setVariable("role", role);

                System.out.println("[Process: " + processInstanceId + "] Successfully fetched complaint data");
                System.out.println("Complaint Data: " + complaintData);

            } else {
                System.err.println("[Process: " + processInstanceId + "] Failed to fetch complaint data or invalid response");
                
                // Set fallback data
                Map<String, Object> fallbackData = new HashMap<>();
                fallbackData.put("complaintId", complaintId);
                fallbackData.put("status", "PENDING");
                fallbackData.put("error", "Failed to fetch complaint details");
                
                execution.setVariable("complaintData", fallbackData);
            }

        } catch (Exception e) {
            System.err.println("[Process: " + processInstanceId + "] Error fetching complaint data: " + e.getMessage());
            e.printStackTrace();

            // Set error data
            Map<String, Object> errorData = new HashMap<>();
            errorData.put("complaintId", complaintId);
            errorData.put("status", "ERROR");
            errorData.put("error", e.getMessage());
            
            execution.setVariable("complaintData", errorData);
        }
    }
}
